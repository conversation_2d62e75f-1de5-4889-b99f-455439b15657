import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: '/api/v1',
  timeout: 10000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response) {
      const { status, data } = error.response
      
      if (status === 401) {
        localStorage.removeItem('token')
        window.location.href = '/login'
        ElMessage.error('登录已过期，请重新登录')
      } else if (status === 403) {
        ElMessage.error('没有权限访问')
      } else if (status === 404) {
        ElMessage.error('请求的资源不存在')
      } else if (status >= 500) {
        ElMessage.error('服务器错误，请稍后重试')
      } else {
        ElMessage.error(data.detail || '请求失败')
      }
    } else {
      ElMessage.error('网络错误，请检查网络连接')
    }
    
    return Promise.reject(error)
  }
)

// 认证相关API
export const authAPI = {
  login: (data) => api.post('/auth/login', data),
  register: (data) => api.post('/auth/register', data)
}

// 用户相关API
export const userAPI = {
  getUsers: () => api.get('/users/'),
  getUser: (id) => api.get(`/users/${id}`),
  createUser: (data) => api.post('/users/', data),
  updateUser: (id, data) => api.put(`/users/${id}`, data),
  deleteUser: (id) => api.delete(`/users/${id}`)
}

// 部门相关API
export const departmentAPI = {
  getDepartments: () => api.get('/departments/'),
  getDepartmentTree: () => api.get('/departments/tree'),
  createDepartment: (data) => api.post('/departments/', data),
  updateDepartment: (id, data) => api.put(`/departments/${id}`, data),
  deleteDepartment: (id) => api.delete(`/departments/${id}`)
}

// 文档相关API
export const documentAPI = {
  getDocuments: (params) => api.get('/documents/', { params }),
  getDocument: (id) => api.get(`/documents/${id}`),
  createDocument: (data) => api.post('/documents/', data),
  updateDocument: (id, data) => api.put(`/documents/${id}`, data),
  deleteDocument: (id) => api.delete(`/documents/${id}`),
  generateDocument: (data) => api.post('/documents/generate', data)
}

// 知识库相关API
export const knowledgeAPI = {
  getKnowledgeClauses: (params) => api.get('/knowledge/', { params }),
  getStandards: () => api.get('/knowledge/standards'),
  createKnowledgeClause: (data) => api.post('/knowledge/', data),
  recommendClauses: (keywords) => api.get('/knowledge/recommend', { params: { keywords } })
}

export default api
