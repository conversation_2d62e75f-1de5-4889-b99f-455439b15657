"""
AI服务模块 - 负责与AI API的交互
"""
import requests
import json
from datetime import datetime
from typing import Dict, List, Optional
from app.core.config import settings


class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.api_url = settings.AI_API_URL
        self.api_key = settings.AI_API_KEY
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
    
    def generate_document_draft(
        self,
        title: str,
        departments: List[str],
        standards: List[str],
        process_data: Optional[Dict] = None
    ) -> str:
        """
        生成文档草稿
        
        Args:
            title: 文档标题
            departments: 涉及部门列表
            standards: 关联标准列表
            process_data: 流程数据
        
        Returns:
            生成的文档内容
        """
        
        # 构建提示词
        prompt = self._build_document_prompt(title, departments, standards, process_data)
        
        # 如果没有配置AI服务，返回模拟内容
        if not self.api_url or not self.api_key:
            return self._generate_mock_document(title, departments, standards)
        
        try:
            # 调用AI API
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json={
                    "model": "gpt-4",
                    "messages": [
                        {"role": "system", "content": "你是一个专业的质量管理体系文件编写专家。"},
                        {"role": "user", "content": prompt}
                    ],
                    "max_tokens": 2000,
                    "temperature": 0.7
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("choices", [{}])[0].get("message", {}).get("content", "")
            else:
                # API调用失败，返回模拟内容
                return self._generate_mock_document(title, departments, standards)
                
        except Exception as e:
            print(f"AI服务调用失败: {e}")
            # 异常情况下返回模拟内容
            return self._generate_mock_document(title, departments, standards)
    
    def recommend_standards(self, keywords: str) -> List[Dict]:
        """
        根据关键词推荐相关标准
        
        Args:
            keywords: 关键词
            
        Returns:
            推荐的标准列表
        """
        # 简化的推荐逻辑，实际项目中可以使用更复杂的算法
        recommendations = []
        
        if "采购" in keywords or "供应商" in keywords:
            recommendations.extend([
                {"standard": "ISO 9001:2015", "clause": "8.4", "title": "外部提供的过程、产品和服务的控制"},
                {"standard": "ISO 9001:2015", "clause": "8.4.1", "title": "总则"}
            ])
        
        if "质量" in keywords or "检验" in keywords:
            recommendations.extend([
                {"standard": "ISO 9001:2015", "clause": "7.1.5", "title": "监视和测量资源"},
                {"standard": "ISO 9001:2015", "clause": "8.6", "title": "产品和服务的放行"}
            ])
        
        return recommendations
    
    def _build_document_prompt(
        self,
        title: str,
        departments: List[str],
        standards: List[str],
        process_data: Optional[Dict] = None
    ) -> str:
        """构建文档生成提示词"""
        
        prompt = f"""
请根据以下信息生成一份专业的质量管理体系文件：

文件标题：{title}
涉及部门：{', '.join(departments)}
关联标准：{', '.join(standards)}
"""
        
        if process_data:
            prompt += f"""
过程信息：
- 输入：{process_data.get('inputs', '')}
- 输出：{process_data.get('outputs', '')}
- 关键绩效指标：{process_data.get('kpis', '')}
"""
        
        prompt += """
请按照以下结构生成文件内容：
1. 目的
2. 范围
3. 职责
4. 工作程序
5. 相关记录

要求：
- 内容专业、准确
- 符合ISO 9001标准要求
- 语言简洁明了
- 具有可操作性
"""
        
        return prompt
    
    def _generate_mock_document(
        self,
        title: str,
        departments: List[str],
        standards: List[str]
    ) -> str:
        """生成模拟文档内容（当AI服务不可用时）"""
        
        content = f"""# {title}

## 1. 目的
本程序旨在规范{title.replace('程序', '').replace('控制', '')}相关活动，确保符合质量管理体系要求。

## 2. 范围
本程序适用于{', '.join(departments)}的相关工作活动。

## 3. 职责
"""
        
        for dept in departments:
            content += f"- {dept}：负责{title.replace('程序', '').replace('控制', '')}相关工作的执行和监督\n"
        
        content += """
## 4. 工作程序
4.1 准备阶段
- 确定工作要求和标准
- 准备必要的资源和工具

4.2 执行阶段
- 按照既定程序执行工作
- 记录执行过程和结果

4.3 检查阶段
- 对执行结果进行检查和验证
- 识别和处理不符合项

4.4 改进阶段
- 分析执行过程中的问题
- 制定改进措施

## 5. 相关记录
- 工作记录表
- 检查记录表
- 不符合项处理记录

---
*本文档由AI智能生成，请根据实际情况进行调整和完善。*
"""
        
        return content

    def generate_document_from_template(self, template_content: str, standards: list, requirements: str = "") -> str:
        """
        基于用户提供的模板生成符合ISO要求的制度文件
        """
        # 构建提示词
        prompt = f"""
请根据以下用户提供的制度模板，生成一份符合ISO标准要求的正式制度文件。

用户模板内容：
{template_content}

适用的ISO标准：
{', '.join(standards) if standards else '通用质量管理标准'}

特殊要求：
{requirements if requirements else '无特殊要求'}

请按照以下要求生成正式的制度文件：

1. 保持用户模板的基本结构和核心内容
2. 补充完善ISO标准要求的必要要素
3. 使用专业的制度文件语言和格式
4. 确保内容的完整性、逻辑性和合规性
5. 添加必要的ISO标准条款引用
6. 完善职责分工和工作流程描述
7. 补充相关记录和表单要求

生成的文件应包含以下标准章节（如果模板中缺少）：
- 目的和范围
- 引用文件
- 术语和定义
- 职责
- 工作程序
- 相关记录
- 附录（如需要）

请确保生成的文件既保持了用户模板的特色，又完全符合ISO标准的要求。
"""

        # 模拟AI生成（实际应用中这里会调用真实的AI API）
        content = f"""
{self._extract_title_from_template(template_content)}

文件编号：[待分配]
版本：V1.0
生效日期：{datetime.now().strftime('%Y年%m月%d日')}
适用标准：{', '.join(standards)}

1. 目的
{self._generate_purpose_from_template(template_content)}

2. 范围
本程序适用于公司相关业务活动的全过程管理，确保符合{', '.join(standards)}标准要求。

3. 引用文件
- {', '.join(standards)}
- 公司质量手册
- 相关法律法规

4. 术语和定义
本程序采用{standards[0] if standards else 'ISO 9001:2015'}中的术语和定义。

{self._enhance_template_content(template_content, standards)}

8. 监控和测量
8.1 建立关键绩效指标(KPI)监控体系
8.2 定期评估程序执行效果
8.3 持续改进程序内容和执行方式

9. 记录控制
9.1 所有相关记录应按照《记录控制程序》要求进行管理
9.2 记录保存期限不少于3年
9.3 记录应真实、准确、完整

10. 附录
附录A：相关记录表单
附录B：流程图
附录C：检查清单

---
本程序经审批后生效，任何修改需按照《文件控制程序》执行。

编制：[编制人]
审核：[审核人]
批准：[批准人]
"""

        return content.strip()

    def _extract_title_from_template(self, template_content: str) -> str:
        """从模板中提取标题"""
        lines = template_content.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith(('1.', '2.', '3.', '一、', '二、', '三、')):
                return line
        return "制度文件"

    def _generate_purpose_from_template(self, template_content: str) -> str:
        """基于模板生成目的章节"""
        if "目的" in template_content:
            # 提取原有目的内容并增强
            lines = template_content.split('\n')
            for i, line in enumerate(lines):
                if "目的" in line:
                    if i + 1 < len(lines):
                        original_purpose = lines[i + 1].strip()
                        return f"{original_purpose}，确保相关活动符合ISO标准要求，持续改进管理效果。"

        return "规范相关业务活动，确保过程的有效性和符合性，持续提升管理水平。"

    def _enhance_template_content(self, template_content: str, standards: list) -> str:
        """增强模板内容，使其符合ISO要求"""
        enhanced_content = template_content

        # 添加ISO标准要求的增强内容
        iso_enhancements = """
5. 职责
5.1 管理者代表：负责程序的建立、实施和持续改进
5.2 过程负责人：负责程序的日常执行和监控
5.3 相关部门：按照程序要求执行相关活动
5.4 内审员：负责程序执行效果的审核

6. 工作程序
6.1 策划阶段
   - 识别相关要求和期望
   - 制定实施计划
   - 配置必要资源

6.2 实施阶段
   - 按照程序要求执行
   - 记录执行过程和结果
   - 及时处理异常情况

6.3 检查阶段
   - 定期监控关键指标
   - 开展内部审核
   - 收集相关方反馈

6.4 改进阶段
   - 分析监控和审核结果
   - 识别改进机会
   - 实施纠正和预防措施

7. 风险管理
7.1 识别相关风险和机遇
7.2 制定风险控制措施
7.3 定期评估风险控制效果
"""

        return iso_enhancements
