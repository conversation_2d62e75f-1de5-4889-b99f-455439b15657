"""
AI服务模块 - 负责与AI API的交互
"""
import requests
import json
from typing import Dict, List, Optional
from app.core.config import settings


class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.api_url = settings.AI_API_URL
        self.api_key = settings.AI_API_KEY
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
    
    def generate_document_draft(
        self,
        title: str,
        departments: List[str],
        standards: List[str],
        process_data: Optional[Dict] = None
    ) -> str:
        """
        生成文档草稿
        
        Args:
            title: 文档标题
            departments: 涉及部门列表
            standards: 关联标准列表
            process_data: 流程数据
        
        Returns:
            生成的文档内容
        """
        
        # 构建提示词
        prompt = self._build_document_prompt(title, departments, standards, process_data)
        
        # 如果没有配置AI服务，返回模拟内容
        if not self.api_url or not self.api_key:
            return self._generate_mock_document(title, departments, standards)
        
        try:
            # 调用AI API
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json={
                    "model": "gpt-4",
                    "messages": [
                        {"role": "system", "content": "你是一个专业的质量管理体系文件编写专家。"},
                        {"role": "user", "content": prompt}
                    ],
                    "max_tokens": 2000,
                    "temperature": 0.7
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("choices", [{}])[0].get("message", {}).get("content", "")
            else:
                # API调用失败，返回模拟内容
                return self._generate_mock_document(title, departments, standards)
                
        except Exception as e:
            print(f"AI服务调用失败: {e}")
            # 异常情况下返回模拟内容
            return self._generate_mock_document(title, departments, standards)
    
    def recommend_standards(self, keywords: str) -> List[Dict]:
        """
        根据关键词推荐相关标准
        
        Args:
            keywords: 关键词
            
        Returns:
            推荐的标准列表
        """
        # 简化的推荐逻辑，实际项目中可以使用更复杂的算法
        recommendations = []
        
        if "采购" in keywords or "供应商" in keywords:
            recommendations.extend([
                {"standard": "ISO 9001:2015", "clause": "8.4", "title": "外部提供的过程、产品和服务的控制"},
                {"standard": "ISO 9001:2015", "clause": "8.4.1", "title": "总则"}
            ])
        
        if "质量" in keywords or "检验" in keywords:
            recommendations.extend([
                {"standard": "ISO 9001:2015", "clause": "7.1.5", "title": "监视和测量资源"},
                {"standard": "ISO 9001:2015", "clause": "8.6", "title": "产品和服务的放行"}
            ])
        
        return recommendations
    
    def _build_document_prompt(
        self,
        title: str,
        departments: List[str],
        standards: List[str],
        process_data: Optional[Dict] = None
    ) -> str:
        """构建文档生成提示词"""
        
        prompt = f"""
请根据以下信息生成一份专业的质量管理体系文件：

文件标题：{title}
涉及部门：{', '.join(departments)}
关联标准：{', '.join(standards)}
"""
        
        if process_data:
            prompt += f"""
过程信息：
- 输入：{process_data.get('inputs', '')}
- 输出：{process_data.get('outputs', '')}
- 关键绩效指标：{process_data.get('kpis', '')}
"""
        
        prompt += """
请按照以下结构生成文件内容：
1. 目的
2. 范围
3. 职责
4. 工作程序
5. 相关记录

要求：
- 内容专业、准确
- 符合ISO 9001标准要求
- 语言简洁明了
- 具有可操作性
"""
        
        return prompt
    
    def _generate_mock_document(
        self,
        title: str,
        departments: List[str],
        standards: List[str]
    ) -> str:
        """生成模拟文档内容（当AI服务不可用时）"""
        
        content = f"""# {title}

## 1. 目的
本程序旨在规范{title.replace('程序', '').replace('控制', '')}相关活动，确保符合质量管理体系要求。

## 2. 范围
本程序适用于{', '.join(departments)}的相关工作活动。

## 3. 职责
"""
        
        for dept in departments:
            content += f"- {dept}：负责{title.replace('程序', '').replace('控制', '')}相关工作的执行和监督\n"
        
        content += """
## 4. 工作程序
4.1 准备阶段
- 确定工作要求和标准
- 准备必要的资源和工具

4.2 执行阶段
- 按照既定程序执行工作
- 记录执行过程和结果

4.3 检查阶段
- 对执行结果进行检查和验证
- 识别和处理不符合项

4.4 改进阶段
- 分析执行过程中的问题
- 制定改进措施

## 5. 相关记录
- 工作记录表
- 检查记录表
- 不符合项处理记录

---
*本文档由AI智能生成，请根据实际情况进行调整和完善。*
"""
        
        return content
