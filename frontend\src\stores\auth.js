import { defineStore } from 'pinia'
import { authAPI } from '@/api'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: localStorage.getItem('token'),
    isAuthenticated: !!localStorage.getItem('token')
  }),

  getters: {
    isLoggedIn: (state) => state.isAuthenticated,
    currentUser: (state) => state.user
  },

  actions: {
    async login(credentials) {
      try {
        const response = await authAPI.login(credentials)
        
        this.token = response.access_token
        this.isAuthenticated = true
        
        localStorage.setItem('token', response.access_token)
        
        // 这里可以获取用户信息
        this.user = {
          username: credentials.username,
          role: 'admin' // 临时硬编码
        }
        
        return response
      } catch (error) {
        this.logout()
        throw error
      }
    },

    logout() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      
      localStorage.removeItem('token')
    },

    async register(userData) {
      try {
        const response = await authAPI.register(userData)
        return response
      } catch (error) {
        throw error
      }
    }
  }
})
