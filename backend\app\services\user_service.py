from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.user import User
from app.schemas.auth import UserLogin
from app.core.security import get_password_hash, verify_password


class UserService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_user_by_username(self, username: str) -> User:
        """根据用户名获取用户"""
        result = await self.db.execute(select(User).where(User.username == username))
        return result.scalar_one_or_none()
    
    async def authenticate_user(self, username: str, password: str) -> User:
        """验证用户登录"""
        user = await self.get_user_by_username(username)
        if not user:
            return None
        if not verify_password(password, user.password_hash):
            return None
        return user
    
    async def create_user(self, user_data: UserLogin) -> User:
        """创建新用户"""
        hashed_password = get_password_hash(user_data.password)
        user = User(
            username=user_data.username,
            password_hash=hashed_password,
            full_name=user_data.username,  # 临时使用用户名作为全名
            role="user"
        )
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        return user
