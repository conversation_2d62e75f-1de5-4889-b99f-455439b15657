{"name": "zhidu-frontend", "version": "1.0.0", "description": "工厂制度文件智能生成与管理平台前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "bpmn-js": "^17.0.2", "@tiptap/vue-3": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "@tiptap/extension-table": "^2.1.13", "@tiptap/extension-image": "^2.1.13", "echarts": "^5.4.3", "vue-echarts": "^6.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.10", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "@vue/eslint-config-prettier": "^9.0.0", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0"}}