version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: zhidu_postgres
    environment:
      POSTGRES_DB: zhidu_db
      POSTGRES_USER: zhidu_user
      POSTGRES_PASSWORD: zhidu_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - zhidu_network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: zhidu_backend
    environment:
      DATABASE_URL: ****************************************************/zhidu_db
      SECRET_KEY: your-production-secret-key
    ports:
      - "8000:8000"
    depends_on:
      - postgres
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
    networks:
      - zhidu_network

  # 前端服务（开发环境）
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: zhidu_frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - zhidu_network

volumes:
  postgres_data:

networks:
  zhidu_network:
    driver: bridge
