from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from app.core.database import get_db
from app.models.department import Department
from app.schemas.department import DepartmentResponse, DepartmentCreate

router = APIRouter()

@router.get("/", response_model=List[DepartmentResponse])
def get_departments(db: Session = Depends(get_db)):
    """获取部门列表"""
    departments = db.query(Department).all()
    return departments

@router.get("/tree")
def get_department_tree(db: Session = Depends(get_db)):
    """获取部门树形结构"""
    departments = db.query(Department).all()

    # 构建树形结构
    dept_dict = {dept.id: {
        "id": dept.id,
        "name": dept.name,
        "parent_id": dept.parent_id,
        "children": []
    } for dept in departments}

    root_depts = []
    for dept in dept_dict.values():
        if dept["parent_id"] is None:
            root_depts.append(dept)
        else:
            parent = dept_dict.get(dept["parent_id"])
            if parent:
                parent["children"].append(dept)

    return root_depts

@router.post("/", response_model=DepartmentResponse)
def create_department(department: DepartmentCreate, db: Session = Depends(get_db)):
    """创建部门"""
    db_department = Department(**department.dict())
    db.add(db_department)
    db.commit()
    db.refresh(db_department)
    return db_department
