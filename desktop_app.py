#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工厂制度文件智能生成与管理平台 - 桌面版
基于tkinter的桌面应用程序
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import requests
import json
import threading
from datetime import datetime

class DocumentGeneratorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("🏭 工厂制度文件智能生成平台")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # API基础URL
        self.api_base = "http://localhost:8000"
        self.token = None
        
        # 创建界面
        self.create_widgets()
        
        # 尝试自动登录
        self.auto_login()
    
    def create_widgets(self):
        # 主标题
        title_frame = tk.Frame(self.root, bg='#667eea', height=80)
        title_frame.pack(fill='x', padx=0, pady=0)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🏭 工厂制度文件智能生成平台", 
                              font=('Microsoft YaHei', 18, 'bold'), 
                              fg='white', bg='#667eea')
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(title_frame, text="基于AI技术的工厂制度文件智能生成与管理系统", 
                                 font=('Microsoft YaHei', 10), 
                                 fg='#e8e8e8', bg='#667eea')
        subtitle_label.pack()
        
        # 主内容区域
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 左侧控制面板
        left_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        left_frame.pack(side='left', fill='y', padx=(0, 10))
        
        # 登录状态
        status_frame = tk.LabelFrame(left_frame, text="🔐 系统状态", font=('Microsoft YaHei', 10, 'bold'))
        status_frame.pack(fill='x', padx=10, pady=10)
        
        self.status_label = tk.Label(status_frame, text="未登录", fg='red', font=('Microsoft YaHei', 9))
        self.status_label.pack(pady=5)
        
        login_btn = tk.Button(status_frame, text="登录系统", command=self.login, 
                             bg='#667eea', fg='white', font=('Microsoft YaHei', 9))
        login_btn.pack(pady=5)
        
        # 文档生成区域
        doc_frame = tk.LabelFrame(left_frame, text="📝 文档生成", font=('Microsoft YaHei', 10, 'bold'))
        doc_frame.pack(fill='x', padx=10, pady=10)
        
        # 文档标题
        tk.Label(doc_frame, text="文档标题:", font=('Microsoft YaHei', 9)).pack(anchor='w', padx=5, pady=(5,0))
        self.title_entry = tk.Entry(doc_frame, font=('Microsoft YaHei', 9))
        self.title_entry.pack(fill='x', padx=5, pady=(0,5))
        self.title_entry.insert(0, "质量检验程序")
        
        # 涉及部门
        tk.Label(doc_frame, text="涉及部门 (用逗号分隔):", font=('Microsoft YaHei', 9)).pack(anchor='w', padx=5, pady=(5,0))
        self.departments_entry = tk.Entry(doc_frame, font=('Microsoft YaHei', 9))
        self.departments_entry.pack(fill='x', padx=5, pady=(0,5))
        self.departments_entry.insert(0, "质量部,生产部,技术部")
        
        # 关联标准
        tk.Label(doc_frame, text="关联标准 (用逗号分隔):", font=('Microsoft YaHei', 9)).pack(anchor='w', padx=5, pady=(5,0))
        self.standards_entry = tk.Entry(doc_frame, font=('Microsoft YaHei', 9))
        self.standards_entry.pack(fill='x', padx=5, pady=(0,5))
        self.standards_entry.insert(0, "ISO 9001:2015 8.5,ISO 9001:2015 8.6")
        
        # 过程输入
        tk.Label(doc_frame, text="过程输入:", font=('Microsoft YaHei', 9)).pack(anchor='w', padx=5, pady=(5,0))
        self.inputs_entry = tk.Entry(doc_frame, font=('Microsoft YaHei', 9))
        self.inputs_entry.pack(fill='x', padx=5, pady=(0,5))
        self.inputs_entry.insert(0, "待检产品,检验标准")
        
        # 过程输出
        tk.Label(doc_frame, text="过程输出:", font=('Microsoft YaHei', 9)).pack(anchor='w', padx=5, pady=(5,0))
        self.outputs_entry = tk.Entry(doc_frame, font=('Microsoft YaHei', 9))
        self.outputs_entry.pack(fill='x', padx=5, pady=(0,5))
        self.outputs_entry.insert(0, "检验报告,合格证明")
        
        # 关键绩效指标
        tk.Label(doc_frame, text="关键绩效指标:", font=('Microsoft YaHei', 9)).pack(anchor='w', padx=5, pady=(5,0))
        self.kpis_entry = tk.Entry(doc_frame, font=('Microsoft YaHei', 9))
        self.kpis_entry.pack(fill='x', padx=5, pady=(0,5))
        self.kpis_entry.insert(0, "合格率≥99%,及时率≥95%")
        
        # 生成按钮
        generate_btn = tk.Button(doc_frame, text="🚀 生成文档", command=self.generate_document,
                               bg='#28a745', fg='white', font=('Microsoft YaHei', 10, 'bold'))
        generate_btn.pack(fill='x', padx=5, pady=10)
        
        # 快速模板
        template_frame = tk.LabelFrame(left_frame, text="📋 快速模板", font=('Microsoft YaHei', 10, 'bold'))
        template_frame.pack(fill='x', padx=10, pady=10)
        
        templates = [
            ("采购控制程序", self.load_purchase_template),
            ("质量检验程序", self.load_quality_template),
            ("生产控制程序", self.load_production_template),
            ("设备管理程序", self.load_equipment_template)
        ]
        
        for name, command in templates:
            btn = tk.Button(template_frame, text=name, command=command,
                           bg='#6c757d', fg='white', font=('Microsoft YaHei', 8))
            btn.pack(fill='x', padx=5, pady=2)
        
        # 右侧文档显示区域
        right_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        right_frame.pack(side='right', fill='both', expand=True)
        
        # 文档标题显示
        self.doc_title_label = tk.Label(right_frame, text="📄 生成的文档将在这里显示", 
                                       font=('Microsoft YaHei', 12, 'bold'), bg='white')
        self.doc_title_label.pack(pady=10)
        
        # 文档内容显示
        self.doc_text = scrolledtext.ScrolledText(right_frame, wrap=tk.WORD, 
                                                 font=('Microsoft YaHei', 10),
                                                 bg='#fafafa', fg='#333')
        self.doc_text.pack(fill='both', expand=True, padx=10, pady=(0, 10))
        
        # 底部按钮
        button_frame = tk.Frame(right_frame, bg='white')
        button_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        save_btn = tk.Button(button_frame, text="💾 保存文档", command=self.save_document,
                           bg='#17a2b8', fg='white', font=('Microsoft YaHei', 9))
        save_btn.pack(side='left', padx=(0, 5))
        
        clear_btn = tk.Button(button_frame, text="🗑️ 清空", command=self.clear_document,
                            bg='#dc3545', fg='white', font=('Microsoft YaHei', 9))
        clear_btn.pack(side='left', padx=5)
        
        # 状态栏
        self.status_bar = tk.Label(self.root, text="就绪", relief=tk.SUNKEN, anchor=tk.W,
                                  font=('Microsoft YaHei', 8), bg='#e9ecef')
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def auto_login(self):
        """自动登录"""
        try:
            response = requests.post(f"{self.api_base}/api/v1/auth/login", 
                                   data={"username": "admin", "password": "admin"})
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                self.status_label.config(text="已登录 (admin)", fg='green')
                self.update_status("✅ 自动登录成功")
            else:
                self.update_status("❌ 自动登录失败")
        except Exception as e:
            self.update_status(f"❌ 连接服务器失败: {str(e)}")
    
    def login(self):
        """手动登录"""
        self.auto_login()
    
    def update_status(self, message):
        """更新状态栏"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_bar.config(text=f"[{timestamp}] {message}")
        self.root.update()
    
    def generate_document(self):
        """生成文档"""
        if not self.token:
            messagebox.showerror("错误", "请先登录系统")
            return
        
        # 获取输入数据
        title = self.title_entry.get().strip()
        departments = [d.strip() for d in self.departments_entry.get().split(',') if d.strip()]
        standards = [s.strip() for s in self.standards_entry.get().split(',') if s.strip()]
        inputs = self.inputs_entry.get().strip()
        outputs = self.outputs_entry.get().strip()
        kpis = self.kpis_entry.get().strip()
        
        if not title:
            messagebox.showerror("错误", "请输入文档标题")
            return
        
        # 准备请求数据
        data = {
            "title": title,
            "departments": departments,
            "standards": standards,
            "process_data": {
                "inputs": inputs,
                "outputs": outputs,
                "kpis": kpis
            }
        }
        
        # 在新线程中生成文档
        self.update_status("🔄 正在生成文档...")
        threading.Thread(target=self._generate_document_thread, args=(data,), daemon=True).start()
    
    def _generate_document_thread(self, data):
        """在后台线程中生成文档"""
        try:
            headers = {"Content-Type": "application/json"}
            if self.token:
                headers["Authorization"] = f"Bearer {self.token}"
            
            response = requests.post(f"{self.api_base}/api/v1/documents/generate",
                                   json=data, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                # 在主线程中更新UI
                self.root.after(0, self._update_document_display, result)
            else:
                error_msg = f"生成失败: {response.status_code}"
                self.root.after(0, lambda: self.update_status(f"❌ {error_msg}"))
                self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
        
        except Exception as e:
            error_msg = f"生成文档时出错: {str(e)}"
            self.root.after(0, lambda: self.update_status(f"❌ {error_msg}"))
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
    
    def _update_document_display(self, result):
        """更新文档显示"""
        self.doc_title_label.config(text=f"📄 {result['title']}")
        
        # 清空并插入新内容
        self.doc_text.delete(1.0, tk.END)
        
        # 添加文档信息
        info = f"文档标题: {result['title']}\n"
        info += f"涉及部门: {', '.join(result['departments'])}\n"
        info += f"关联标准: {', '.join(result['standards'])}\n"
        info += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        info += "=" * 50 + "\n\n"
        
        self.doc_text.insert(tk.END, info)
        self.doc_text.insert(tk.END, result['content'])
        
        self.update_status("✅ 文档生成完成")
    
    def save_document(self):
        """保存文档"""
        content = self.doc_text.get(1.0, tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "没有可保存的文档内容")
            return
        
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.update_status(f"✅ 文档已保存到: {filename}")
                messagebox.showinfo("成功", f"文档已保存到:\n{filename}")
            except Exception as e:
                error_msg = f"保存失败: {str(e)}"
                self.update_status(f"❌ {error_msg}")
                messagebox.showerror("错误", error_msg)
    
    def clear_document(self):
        """清空文档"""
        self.doc_text.delete(1.0, tk.END)
        self.doc_title_label.config(text="📄 生成的文档将在这里显示")
        self.update_status("🗑️ 文档已清空")
    
    # 模板加载方法
    def load_purchase_template(self):
        """加载采购控制程序模板"""
        self.title_entry.delete(0, tk.END)
        self.title_entry.insert(0, "采购控制程序")
        self.departments_entry.delete(0, tk.END)
        self.departments_entry.insert(0, "采购部,质量部,财务部")
        self.standards_entry.delete(0, tk.END)
        self.standards_entry.insert(0, "ISO 9001:2015 8.4")
        self.inputs_entry.delete(0, tk.END)
        self.inputs_entry.insert(0, "采购申请单,供应商信息")
        self.outputs_entry.delete(0, tk.END)
        self.outputs_entry.insert(0, "采购订单,合格供应商名录")
        self.kpis_entry.delete(0, tk.END)
        self.kpis_entry.insert(0, "及时率≥95%,供应商合格率≥98%")
    
    def load_quality_template(self):
        """加载质量检验程序模板"""
        self.title_entry.delete(0, tk.END)
        self.title_entry.insert(0, "质量检验程序")
        self.departments_entry.delete(0, tk.END)
        self.departments_entry.insert(0, "质量部,生产部,技术部")
        self.standards_entry.delete(0, tk.END)
        self.standards_entry.insert(0, "ISO 9001:2015 8.5,ISO 9001:2015 8.6")
        self.inputs_entry.delete(0, tk.END)
        self.inputs_entry.insert(0, "待检产品,检验标准")
        self.outputs_entry.delete(0, tk.END)
        self.outputs_entry.insert(0, "检验报告,合格证明")
        self.kpis_entry.delete(0, tk.END)
        self.kpis_entry.insert(0, "合格率≥99%,及时率≥100%")
    
    def load_production_template(self):
        """加载生产控制程序模板"""
        self.title_entry.delete(0, tk.END)
        self.title_entry.insert(0, "生产过程控制程序")
        self.departments_entry.delete(0, tk.END)
        self.departments_entry.insert(0, "生产部,质量部,技术部,设备部")
        self.standards_entry.delete(0, tk.END)
        self.standards_entry.insert(0, "ISO 9001:2015 8.5")
        self.inputs_entry.delete(0, tk.END)
        self.inputs_entry.insert(0, "生产计划,技术文件,原材料")
        self.outputs_entry.delete(0, tk.END)
        self.outputs_entry.insert(0, "合格产品,生产记录")
        self.kpis_entry.delete(0, tk.END)
        self.kpis_entry.insert(0, "产品合格率≥99%,生产效率≥95%")
    
    def load_equipment_template(self):
        """加载设备管理程序模板"""
        self.title_entry.delete(0, tk.END)
        self.title_entry.insert(0, "设备管理程序")
        self.departments_entry.delete(0, tk.END)
        self.departments_entry.insert(0, "设备部,生产部,质量部")
        self.standards_entry.delete(0, tk.END)
        self.standards_entry.insert(0, "ISO 9001:2015 7.1.3")
        self.inputs_entry.delete(0, tk.END)
        self.inputs_entry.insert(0, "设备清单,维护计划")
        self.outputs_entry.delete(0, tk.END)
        self.outputs_entry.insert(0, "设备状态报告,维护记录")
        self.kpis_entry.delete(0, tk.END)
        self.kpis_entry.insert(0, "设备完好率≥98%,故障率≤2%")

def main():
    root = tk.Tk()
    app = DocumentGeneratorApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
