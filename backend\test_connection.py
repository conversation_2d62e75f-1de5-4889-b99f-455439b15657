#!/usr/bin/env python3
"""
测试服务器连接
"""
import requests
import time
import sys

def test_connection():
    print("🔍 测试服务器连接...")
    
    # 等待服务器启动
    for i in range(10):
        try:
            print(f"尝试连接 ({i+1}/10)...")
            response = requests.get("http://localhost:8000", timeout=3)
            
            if response.status_code == 200:
                print("✅ 服务器连接成功!")
                print(f"状态码: {response.status_code}")
                print(f"响应: {response.json()}")
                
                # 测试API文档
                try:
                    docs_response = requests.get("http://localhost:8000/docs", timeout=3)
                    print(f"✅ API文档可访问: {docs_response.status_code}")
                except:
                    print("⚠️  API文档无法访问")
                
                # 测试健康检查
                try:
                    health_response = requests.get("http://localhost:8000/health", timeout=3)
                    print(f"✅ 健康检查: {health_response.status_code} - {health_response.json()}")
                except:
                    print("⚠️  健康检查无法访问")
                
                return True
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 连接失败，等待服务器启动...")
            time.sleep(2)
        except Exception as e:
            print(f"❌ 错误: {e}")
            time.sleep(2)
    
    print("❌ 无法连接到服务器")
    return False

def check_port():
    """检查端口是否被占用"""
    import socket
    
    print("🔍 检查端口8000...")
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', 8000))
    sock.close()
    
    if result == 0:
        print("✅ 端口8000正在被使用")
        return True
    else:
        print("❌ 端口8000未被使用")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 服务器连接测试")
    print("=" * 50)
    
    # 检查端口
    port_in_use = check_port()
    
    if port_in_use:
        # 测试连接
        success = test_connection()
        
        if success:
            print("\n🎉 服务器运行正常!")
            print("📍 请在浏览器中访问: http://localhost:8000")
            print("📚 API文档: http://localhost:8000/docs")
        else:
            print("\n⚠️  服务器可能有问题")
    else:
        print("\n❌ 服务器未启动，请先启动服务器")
        print("运行命令: python start_server.py")
