@echo off
title 工厂制度文件智能生成平台 - 完整系统启动器

echo ========================================
echo 🏭 工厂制度文件智能生成平台
echo ========================================
echo 正在启动完整系统...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 安装依赖
echo 📦 安装/检查依赖包...
cd backend
pip install -r requirements.txt >nul 2>&1
cd ..
pip install requests >nul 2>&1
echo ✅ 依赖包检查完成
echo.

REM 启动后端服务
echo 🚀 启动后端API服务...
cd backend
start "后端API服务" cmd /k "echo 🔧 后端API服务正在运行... && echo 📍 API地址: http://localhost:8000 && echo 📚 API文档: http://localhost:8000/docs && echo. && python main.py"
cd ..

REM 等待后端服务启动
echo ⏳ 等待后端服务启动...
timeout /t 3 /nobreak >nul

REM 测试后端服务
echo 🔍 测试后端服务连接...
python -c "import requests; import time; time.sleep(2); r = requests.get('http://localhost:8000/health'); print('✅ 后端服务状态正常' if r.status_code == 200 else '❌ 后端服务连接失败')" 2>nul

echo.
echo 🖥️ 启动桌面应用程序...
echo.
echo ========================================
echo 📋 使用说明:
echo 1. 桌面应用会自动连接到后端服务
echo 2. 默认账号: admin / admin (自动登录)
echo 3. 可以使用快速模板或自定义输入
echo 4. 生成的文档可以保存到本地文件
echo 5. 关闭桌面应用不会影响后端服务
echo ========================================
echo.

REM 启动桌面应用
python desktop_app.py

echo.
echo 🔚 桌面应用程序已关闭
echo 💡 后端服务仍在运行，您可以:
echo    - 重新运行此脚本启动桌面应用
echo    - 访问 http://localhost:8000 使用网页版
echo    - 访问 http://localhost:8000/docs 查看API文档
echo.
pause
