#!/usr/bin/env python3
"""
简单的服务器启动脚本
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("=" * 60)
print("🚀 启动工厂制度文件智能生成与管理平台")
print("=" * 60)

try:
    print("📦 导入模块...")
    import uvicorn
    from main import app
    
    print("✅ 模块导入成功")
    print("🌐 启动服务器...")
    print("📍 地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("💡 默认账号: admin / admin")
    print("=" * 60)
    print("按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    # 启动服务器
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=True
    )
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装所有依赖包:")
    print("pip install fastapi uvicorn sqlalchemy bcrypt python-jose passlib")
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    print("请检查端口8000是否被占用")

print("\n👋 服务器已停止")
