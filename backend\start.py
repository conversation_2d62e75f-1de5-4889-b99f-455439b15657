"""
启动脚本 - 解决Windows下输出缓冲问题
"""
import os
import sys
import subprocess

def start_server():
    """启动服务器"""
    print("=" * 60)
    print("工厂制度文件智能生成与管理平台")
    print("=" * 60)
    print("正在启动服务器...")
    print("服务器地址: http://localhost:8000")
    print("API文档: http://localhost:8000/docs")
    print("管理界面: http://localhost:8000/admin")
    print("=" * 60)
    print("按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    # 设置环境变量强制输出
    env = os.environ.copy()
    env['PYTHONUNBUFFERED'] = '1'
    
    try:
        # 使用subprocess启动，确保输出可见
        subprocess.run([
            sys.executable, "main.py"
        ], env=env, check=True)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    start_server()
