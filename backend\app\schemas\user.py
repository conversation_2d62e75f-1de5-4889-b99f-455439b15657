from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from app.models.user import UserRole


class UserBase(BaseModel):
    username: str
    full_name: str
    position: Optional[str] = None
    employee_id: Optional[str] = None
    department_id: Optional[int] = None
    role: UserRole = UserRole.USER


class UserCreate(UserBase):
    password: str


class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    position: Optional[str] = None
    employee_id: Optional[str] = None
    department_id: Optional[int] = None
    role: Optional[UserRole] = None


class UserResponse(UserBase):
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
