@echo off
title AI制度体系文件生成系统 - 修复版

echo ========================================
echo 🏛️ AI制度体系文件生成系统 - 修复版
echo ========================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 🚀 启动AI制度体系文件生成系统...
echo.
echo ========================================
echo 📋 修复内容:
echo   ✅ 修复了模块导入问题
echo   ✅ 实现了所有基本功能
echo   ✅ 添加了简化版AI服务
echo   ✅ 完善了用户交互功能
echo.
echo 🎯 主要功能:
echo   • AI智能生成制度文件
echo   • 专业的文档编辑器
echo   • 合规性检查功能
echo   • 文档导入导出
echo   • 快速模板生成
echo.
echo 📝 使用方法:
echo   1. 填写组织基本信息
echo   2. 输入文档框架和关键词
echo   3. 点击"开始AI智能生成"
echo   4. 在编辑器中完善文档
echo   5. 进行合规性检查
echo   6. 导出最终文档
echo.
echo ========================================
echo.

REM 启动修复版系统
python professional_document_generator.py

echo.
echo 🔚 系统已关闭
pause
