# 工厂制度文件智能生成与管理平台 V2.0

## 项目简介

本平台是一个基于AI技术的工厂制度文件智能生成与管理系统，能够根据组织架构、人员信息和ISO标准、法律法规要求，自动生成各类管理文件，包括：

- 程序制度文件
- 作业指导书
- 管理方案
- 表单模板
- 过程准则

## 技术架构

### 前端
- **框架**: Vue 3 + Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **流程建模**: BPMN.js
- **富文本编辑**: Tiptap

### 后端
- **框架**: FastAPI (Python)
- **数据库**: PostgreSQL
- **ORM**: SQLAlchemy
- **认证**: JWT
- **API文档**: Swagger/OpenAPI

### AI集成
- 支持多种AI模型接入
- 智能文档生成
- 标准条款推荐
- 合规性分析

## 项目结构

```
zhidu/
├── frontend/          # 前端Vue项目
├── backend/           # 后端FastAPI项目
├── database/          # 数据库脚本和迁移文件
├── docs/              # 项目文档
├── docker/            # Docker配置文件
└── README.md
```

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.9+
- PostgreSQL 13+

### 安装依赖

#### 前端
```bash
cd frontend
npm install
```

#### 后端
```bash
cd backend
pip install -r requirements.txt
```

### 运行项目

#### 后端开发服务器

**方法1: 使用main.py启动（推荐）**
```bash
cd backend
python main.py
```

**方法2: 使用启动脚本（Windows）**
```bash
cd backend
python start.py
# 或者双击 start.bat
```

**方法3: 使用uvicorn命令**
```bash
cd backend
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

启动成功后访问：
- 主页: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

#### 前端开发服务器（需要先安装Node.js）
```bash
cd frontend
npm install
npm run dev
```

## 功能特性

- ✅ 可视化流程建模
- ✅ AI智能文档生成
- ✅ 三级文件体系管理
- ✅ 组织架构管理
- ✅ 知识库管理
- ✅ 版本控制
- ✅ 审批流程
- ✅ 合规性分析

## 开发指南

详细的开发指南请参考 `docs/` 目录下的文档：

- [开发者编写文档.txt](./开发者编写文档.txt)
- [UI设计方案.txt](./UI设计方案.txt)
- [用户操作指南.txt](./用户操作指南.txt)

## 许可证

本项目采用 MIT 许可证。
