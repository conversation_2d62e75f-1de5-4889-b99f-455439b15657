# 🏭 基于模板的AI制度文件生成器 - 使用说明

## 🎯 软件简介

这是一个专门为工厂制度文件管理设计的AI生成工具。您只需要提供现有的制度模板，AI就会自动生成符合ISO标准要求的正式制度文件。

### ✨ 核心功能
- **模板输入**: 支持文本输入或文件加载
- **ISO标准选择**: 支持多种ISO标准（9001、14001、45001、27001）
- **AI智能生成**: 基于模板自动生成符合标准的制度文件
- **文件管理**: 保存、复制、导出生成的文件

## 🚀 快速开始

### 1. 启动软件
双击运行 `start_template_app.bat`

系统会自动：
- 检查Python环境
- 安装必要依赖
- 启动后端服务（如果未运行）
- 打开桌面应用程序

### 2. 界面介绍

```
┌─────────────────────────────────────────────────────────────────┐
│                🏭 基于模板的AI制度文件生成器                      │
├─────────────────────────────┬───────────────────────────────────┤
│        左侧：模板输入区        │        右侧：生成结果区            │
│                             │                                 │
│  🔐 系统状态                 │  📄 生成的制度文件               │
│  📝 制度模板输入              │                                 │
│  📊 ISO标准选择              │  [生成的文档内容显示]             │
│  🚀 生成按钮                 │                                 │
│                             │  💾 保存  📋 复制  📤 导出        │
└─────────────────────────────┴───────────────────────────────────┘
```

## 📋 详细使用步骤

### 步骤1: 准备模板内容

#### 方法A: 直接输入
在左侧"制度模板输入"区域直接输入或粘贴您的制度内容

#### 方法B: 加载文件
1. 点击"📁 加载模板文件"按钮
2. 选择您的制度文件（支持.txt格式）
3. 文件内容会自动加载到输入区域

#### 方法C: 使用示例
点击"📋 示例模板"按钮，加载预设的采购控制程序示例

### 步骤2: 选择ISO标准

在"📊 ISO标准要求"区域，勾选适用的标准：
- ✅ **ISO 9001:2015** - 质量管理体系（默认选中）
- ☐ **ISO 14001:2015** - 环境管理体系
- ☐ **ISO 45001:2018** - 职业健康安全
- ☐ **ISO 27001:2013** - 信息安全管理

### 步骤3: 生成制度文件

点击"🚀 生成符合ISO要求的制度文件"按钮

AI会：
1. 分析您的模板内容
2. 识别现有结构和要素
3. 补充ISO标准要求的内容
4. 生成完整的正式制度文件

### 步骤4: 查看和保存结果

生成完成后，右侧会显示：
- 📄 文档标题和基本信息
- 📝 完整的制度文件内容
- ⏰ 生成时间

您可以：
- **💾 保存文档**: 保存为文本文件
- **📋 复制内容**: 复制到剪贴板
- **📤 导出Word**: 导出为Word文档（开发中）

## 🎯 使用示例

### 示例1: 基于简单模板生成采购程序

**输入模板**：
```
采购控制程序

1. 目的
规范公司采购活动

2. 适用范围
所有采购活动

3. 职责
采购部：负责采购
质量部：负责检验

4. 工作程序
4.1 需求确认
4.2 供应商选择
4.3 订单下达
4.4 验收入库
```

**选择标准**: ISO 9001:2015

**生成结果**: AI会自动补充完善为包含以下内容的正式文件：
- 完整的目的和范围描述
- 引用文件和术语定义
- 详细的职责分工
- 标准化的工作程序
- 风险管理要求
- 监控和测量方法
- 记录控制要求
- 相关表单和附录

### 示例2: 基于详细模板生成质量程序

**输入模板**：
```
产品质量控制程序

目的：确保产品质量符合要求
范围：从原料到成品的全过程质量控制

职责：
- 质量部：制定质量标准，实施检验
- 生产部：按标准生产，配合检验
- 技术部：提供技术支持

程序：
1. 原料检验
2. 过程控制
3. 成品检验
4. 不合格品处理

记录：检验记录、不合格品记录
```

**选择标准**: ISO 9001:2015 + ISO 14001:2015

**生成结果**: AI会保持原有结构，并增强为符合双标准要求的完整程序文件。

## 💡 使用技巧

### 1. 模板准备建议
- **保持清晰结构**: 使用标题、编号等明确的层次结构
- **包含核心要素**: 目的、范围、职责、程序等基本要素
- **描述具体流程**: 详细描述关键工作步骤
- **明确职责分工**: 清楚说明各部门/岗位职责

### 2. ISO标准选择
- **单一体系**: 选择一个主要标准（如ISO 9001）
- **整合体系**: 可选择多个标准进行整合管理
- **行业特色**: 根据行业特点选择相关标准

### 3. 结果优化
- **人工审核**: AI生成的内容需要人工审核和调整
- **本地化调整**: 根据公司实际情况修改具体内容
- **持续改进**: 根据使用效果不断优化模板

## ⚠️ 注意事项

### 1. 模板质量
- 模板内容越详细，生成效果越好
- 建议提供完整的业务流程描述
- 避免过于简单或模糊的描述

### 2. 标准理解
- AI会根据ISO标准要求补充内容
- 生成的内容需要结合实际情况调整
- 建议有ISO标准基础知识的人员使用

### 3. 结果使用
- 生成的文件是草稿，需要人工审核
- 应根据公司实际情况进行调整
- 正式发布前需要经过内部审批流程

## 🔧 故障排除

### 1. 软件无法启动
- 检查Python是否正确安装
- 确认网络连接正常
- 重新运行启动脚本

### 2. 生成失败
- 检查模板内容是否为空
- 确认至少选择了一个ISO标准
- 查看错误提示信息

### 3. 保存失败
- 检查文件保存路径权限
- 确认磁盘空间充足
- 尝试保存到其他位置

## 🎉 开始使用

现在您可以：

1. **双击 `start_template_app.bat`** 启动软件
2. **加载示例模板** 或 **输入您的模板**
3. **选择适用的ISO标准**
4. **点击生成按钮** 体验AI智能生成
5. **保存生成的制度文件** 用于实际工作

享受高效的制度文件生成体验！🚀

---

**技术支持**: 如有问题，请检查命令行窗口的错误信息或重启应用程序。
