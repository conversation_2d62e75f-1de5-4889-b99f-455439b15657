工厂制度文件智能生成与管理平台 V2.0 - 用户操作指导书

文档编号: GZ-UM-002
版本: 2.0
生效日期: YYYY-MM-DD

1. 引言

1.1 目的
本平台旨在通过智能化、可视化的方式，帮助工厂管理人员构建、维护和优化一套动态、合规的管理体系。平台深度融合了AI大模型技术，能够自动生成、关联和审查三级制度文件，将繁琐的文档工作转变为高效的流程管理，确保体系的完整性、一致性与持续有效性。

1.2 适用范围
本指导书适用于所有使用本平台的用户，包括系统管理员、体系工程师、部门经理及相关岗位人员。

1.3 核心概念

三级文件体系: 包括管理手册（一级）、程序文件（二级）、作业指导书/管理方案/表单（三级）。本平台重点在于智能生成与管理二、三级文件。

智能知识库: 存储法律法规、ISO等标准条款，并附加了风险点、建议职责等元数据，是平台AI智慧的来源。

可视化流程建模: 使用拖拽式流程图替代传统表格，直观地定义跨部门业务流程。

过程准则: 定义一个过程的核心要素，是AI生成文件的基础，包括输入、输出、活动、职责、KPI等。

2. 初次使用与系统配置

在正式使用前，系统管理员需要完成一次性基础配置。

2.1 系统初始化
首次启动平台，系统将引导您完成：

设置公司信息: 输入公司全称、简称、Logo。这些信息将自动应用于所有文件的页眉页脚。

创建管理员账号: 设置您的管理员登录名和密码。

2.2 连接AI服务 (核心配置)
为激活平台的智能功能，必须配置AI服务。

登录后，进入主界面右上角的 【系统设置】->【AI服务配置】。

填写配置信息:

API接入点地址 (API Endpoint): 输入您的AI中转API服务的URL。

API密钥 (API Key): 输入用于访问该服务的身份验证密钥。

AI模型 (Model): 从下拉列表中选择一个模型（如 gpt-4-turbo）。

测试与保存:

点击 【测试连接】，确保系统能成功与AI服务通信。

成功后，点击 【保存】。

2.3 同步基础数据

进入 【系统设置】->【数据集成】。

组织架构与人员:

自动同步 (推荐): 配置与公司HR系统、钉钉或企业微信的API接口，实现组织架构和人员名单的每日自动同步。

手动导入: 若无API，可点击“导入组织架构”和“导入人员名单”，上传指定格式的 .json 或 .csv 文件。

管理知识库:

进入 【知识库管理】 模块。

系统已预置常用标准（如ISO 9001）。您可以在此添加、编辑或导入新的法律法规和标准条款。

3. 核心操作流程：从无到有创建一份程序文件

以创建《采购控制程序》为例，体验平台的完整流程。

在主界面点击醒目的 【+ 新建文件】 按钮。

在弹出的窗口中，选择文件类型：【程序文件】。

文件名称: 输入 采购控制程序。

涉及部门: 从组织架构树中勾选 采购部、质量部、仓库、生产部。

关联标准/法规:

AI智能推荐: 系统根据“采购”关键词，会自动从知识库中推荐 ISO 9001: 8.4 外部提供的过程、产品和服务的控制 相关条款。

手动添加: 您可以勾选推荐的条款，或手动搜索并添加其他法规要求。

系统将跳转到 可视化流程建模器 界面。

设置过程所有者: 在右侧“过程属性”面板中，从人员列表中选择 采购部经理 作为“过程所有者”。

定义输入/输出: 在属性面板中，填写过程的输入（如：物料需求申请单）和输出（如：采购订单、进料检验报告）。

绘制流程图:

界面左侧是工具箱（任务、判断、开始/结束等），中部是画布，画布上已按您选择的部门划分好 “泳道”。

拖拽任务: 从工具箱拖拽一个“任务”方块到采购部泳道，命名为“评审采购申请”。

分配职责: 点击该任务，在右侧属性面板的“负责岗位”中选择 采购员。

关联表单: 在“关联记录”中，选择或新建 《采购申请评审记录》 表单。

连接流程: 按照实际业务逻辑，依次拖入“选择供应商”、“下达订单”、“IQC检验”（拖入质量部泳道）等任务，并用箭头连接起来。

添加判断: 当需要决策时（如“金额是否大于1万元？”），拖入一个“判断”菱形，并引出“是/否”两条分支，分别连接到后续的不同流程（如“是”->“经理审批”）。

完成流程图绘制后，点击右上角的 【生成文件草稿】 按钮。

AI开始工作: 平台会将您定义的全部信息（文件名称、过程准则、流程图等）发送给AI大模型。

预览与微调:

几秒钟后，系统会返回一份完整的程序文件草稿，并在富文本编辑器中打开。

AI已自动完成:

撰写了专业的“目的”、“范围”和“职责”章节。

根据您绘制的流程图，生成了逻辑清晰的“工作程序”文字描述，并插入了该流程图。

自动生成了“相关记录”列表。

填充了页眉、页脚和文件编号。

您可以在此编辑器中进行最后的文字润色和格式调整。

提交审批: 审阅无误后，点击 【提交审批】。系统会根据预设的审批流（例如：部门经理 -> 体系主管 -> 总经理），自动将审批任务推送给相关人员。

发布文件: 审批通过后，文件状态变为“已发布”，自动归入 【文件库】，并生成正式的版本号 (V1.0)。

自动分发: 系统会自动向文件中涉及的所有岗位人员发送通知，提醒他们学习新版文件。

4. 文件库与体系维护

【文件库】 是您管理所有制度文件的中央枢纽。

文件检索: 提供强大的搜索功能，可按文件名、文件编号、关键词、关联标准等快速查找文件。

版本控制:

当您需要修订一份已发布的文件时，系统会自动创建新的草稿版本（如 V1.1），同时将旧版（V1.0）归档，确保了版本历史的可追溯性。

智能关联提醒:

如果您修改了《合格供应商名录》这个表单，系统会自动检测到所有引用了此表单的程序文件（如《采购控制程序》），并向文件所有者发送“引用文件已更新，请确认是否需要修订”的提醒。

合规性差距分析:

在文件库主页，点击 【合规性审查】。

选择一个标准（如 ISO 9001:2015），系统会生成一份报告，清晰地展示哪些标准条款尚未被任何文件覆盖，帮助您轻松应对内外部审核。

体系仪表盘:

文件库首页的仪表盘，以图表形式直观展示体系的健康状况：待办审批、待阅文件、文件到期预警、合规覆盖率等。

5. 附录

5.1 文件状态说明

草稿 (Draft): 文件正在创建或修订中，仅创建者可见。

待审批 (In Review): 文件已提交，正在审批流程中。

已发布 (Published): 经审批生效的正式文件。

已归档 (Archived): 已被新版本替代的旧文件。

5.2 技术支持
如遇任何操作问题，请联系您的系统管理员或查阅在线帮助文档。

这份V2.0版本的指导书，为你描绘了一个功能强大且体验流畅的智能平台蓝图。你可以将每个章节、每个步骤拆解成更小的开发任务，然后利用VS Code的AI编程功能逐一实现。祝你开发成功！