# 🎯 工厂制度文件智能生成与管理平台 - 用户使用指南

## 🚀 快速开始

### 1. 访问平台
在浏览器中打开：http://localhost:8000

您将看到一个美观的主页，包含平台介绍和功能说明。

### 2. 系统登录
在主页的登录区域：
- **用户名**: admin
- **密码**: admin
- 点击"登录"按钮

登录成功后，您就可以使用所有功能了。

## 🎨 主要功能

### 1. 🤖 AI智能文档生成

#### 体验文档生成
1. 在主页点击"🚀 体验文档生成"按钮
2. 系统会自动生成一个《采购控制程序》示例
3. 您可以看到AI生成的专业文档内容

#### 自定义文档生成
通过API接口，您可以生成自定义文档：

**请求示例**：
```bash
curl -X POST "http://localhost:8000/api/v1/documents/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "质量检验程序",
    "departments": ["质量部", "生产部"],
    "standards": ["ISO 9001:2015 8.6"],
    "process_data": {
      "inputs": "待检产品",
      "outputs": "检验报告",
      "kpis": "合格率≥98%"
    }
  }'
```

### 2. 📚 API文档浏览

点击主页的"📚 API文档"按钮，您可以：
- 查看所有可用的API接口
- 在线测试API功能
- 了解请求和响应格式

### 3. 🔍 系统状态监控

点击"🔍 系统状态"按钮，检查：
- 服务器运行状态
- 系统健康状况

## 📋 支持的文档类型

### 1. 程序文件
- 质量管理程序
- 采购控制程序
- 生产控制程序
- 设备管理程序
- 人力资源程序

### 2. 作业指导书
- 操作指导书
- 检验指导书
- 维护指导书
- 安全操作指导

### 3. 管理方案
- 质量管理方案
- 环境管理方案
- 安全管理方案
- 应急预案

### 4. 表单模板
- 记录表单
- 检查表单
- 评审表单
- 审核表单

## 🎯 使用场景

### 场景1: 新建质量程序
```json
{
  "title": "产品质量控制程序",
  "departments": ["质量部", "生产部", "技术部"],
  "standards": ["ISO 9001:2015 8.5", "ISO 9001:2015 8.6"],
  "process_data": {
    "inputs": "生产计划、技术文件",
    "outputs": "合格产品、质量记录",
    "kpis": "产品合格率≥99%、客户满意度≥95%"
  }
}
```

### 场景2: 创建采购程序
```json
{
  "title": "供应商管理程序",
  "departments": ["采购部", "质量部", "财务部"],
  "standards": ["ISO 9001:2015 8.4"],
  "process_data": {
    "inputs": "采购需求、供应商信息",
    "outputs": "合格供应商名录、采购合同",
    "kpis": "供应商合格率≥95%、交付及时率≥98%"
  }
}
```

### 场景3: 建立检验程序
```json
{
  "title": "进料检验程序",
  "departments": ["质量部", "仓库", "采购部"],
  "standards": ["ISO 9001:2015 7.1.5", "ISO 9001:2015 8.6"],
  "process_data": {
    "inputs": "进料通知单、检验标准",
    "outputs": "检验报告、合格证明",
    "kpis": "检验及时率≥100%、漏检率≤0.1%"
  }
}
```

## 🔧 高级功能

### 1. 知识库查询
```bash
# 获取所有标准
curl "http://localhost:8000/api/v1/knowledge/standards"

# 搜索相关条款
curl "http://localhost:8000/api/v1/knowledge/recommend?keywords=采购"
```

### 2. 部门管理
```bash
# 获取部门列表
curl "http://localhost:8000/api/v1/departments/"

# 获取部门树形结构
curl "http://localhost:8000/api/v1/departments/tree"
```

### 3. 用户管理
```bash
# 获取用户列表
curl "http://localhost:8000/api/v1/users/"
```

## 💡 使用技巧

### 1. 文档标题命名
- 使用清晰、具体的标题
- 包含关键业务词汇
- 例如："采购控制程序"、"质量检验指导书"

### 2. 部门选择
- 选择所有相关部门
- 确保职责分工清晰
- 考虑跨部门协作

### 3. 标准关联
- 选择最相关的ISO条款
- 可以关联多个标准
- 确保合规性要求

### 4. 过程定义
- 明确输入和输出
- 设定可量化的KPI
- 考虑风险控制点

## 🚨 注意事项

### 1. 数据安全
- 定期备份重要数据
- 保护登录凭据
- 限制访问权限

### 2. 内容审核
- AI生成的内容需要人工审核
- 根据实际情况调整内容
- 确保符合公司具体要求

### 3. 版本管理
- 保留文档版本历史
- 记录修改原因
- 及时更新相关人员

## 🔄 工作流程建议

### 1. 文档创建流程
1. 确定文档需求
2. 收集相关信息
3. 使用AI生成初稿
4. 人工审核和修改
5. 内部评审
6. 正式发布

### 2. 文档维护流程
1. 定期评审
2. 收集反馈
3. 识别改进点
4. 更新文档
5. 重新发布

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **查看API文档**: http://localhost:8000/docs
2. **检查系统状态**: http://localhost:8000/health
3. **查看错误日志**: 检查命令行输出
4. **重启服务**: 按Ctrl+C停止，然后重新运行

## 🎉 开始使用

现在您已经了解了平台的基本使用方法，可以开始创建您的第一个智能制度文件了！

记住：这个平台的目标是帮助您快速生成专业、合规的制度文件，提高工作效率，确保质量管理体系的有效运行。
