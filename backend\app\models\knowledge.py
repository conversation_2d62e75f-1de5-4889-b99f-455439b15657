from sqlalchemy import Column, Integer, String, Text
from sqlalchemy.dialects.postgresql import ARRAY

from app.core.database import Base


class KnowledgeClause(Base):
    __tablename__ = "knowledge_clauses"

    id = Column(Integer, primary_key=True, index=True)
    standard_name = Column(String(100), nullable=False)  # 如 "ISO 9001:2015"
    clause_number = Column(String(20), nullable=False)   # 如 "8.4.1"
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    keywords = Column(Text)  # 以逗号分隔的关键词
    meta_data = Column(Text)  # JSON格式存储元数据（建议职责、风险点等）
