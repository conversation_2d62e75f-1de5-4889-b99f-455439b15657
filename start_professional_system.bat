@echo off
title AI制度体系文件生成系统 - 专业版

echo ========================================
echo 🏛️ AI制度体系文件生成系统 - 专业版
echo ========================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 安装依赖
echo 📦 检查和安装依赖包...
pip install tkinter >nul 2>&1
echo ✅ 依赖包检查完成
echo.

echo 🚀 启动AI制度体系文件生成系统...
echo.
echo ========================================
echo 📋 系统功能特色:
echo.
echo 🧠 智能特性:
echo   • 基于框架/关键词的AI智能生成
echo   • 上下文感知 (组织类型/行业特性)
echo   • 自动合规性检查
echo   • 多语言支持
echo.
echo 📊 文档管理:
echo   • 版本控制和差异比较
echo   • 多格式导出 (Word/PDF/Markdown)
echo   • 文档属性管理
echo.
echo 👥 协作功能:
echo   • 多角色协同编辑
echo   • 审批工作流
echo   • 电子签名集成
echo.
echo 📚 知识库:
echo   • 行业标准模板库
echo   • 法律条文数据库
echo   • 最佳实践案例库
echo.
echo ========================================
echo.

REM 启动专业系统
python professional_document_generator.py

echo.
echo 🔚 系统已关闭
pause
