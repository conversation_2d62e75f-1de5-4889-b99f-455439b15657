# 🚀 如何启动工厂制度文件智能生成与管理平台

## 问题解决方案

如果您遇到"打不开网站"的问题，请按照以下步骤操作：

## 方法1: 快速启动（推荐）

### 1. 打开命令行
- 按 `Win + R`，输入 `cmd`，按回车
- 或者在开始菜单搜索"命令提示符"

### 2. 进入项目目录
```bash
cd C:\Users\<USER>\Documents\augment-projects\zhidu\backend
```

### 3. 启动服务器
```bash
python main.py
```

### 4. 访问网站
在浏览器中打开以下地址：
- **主页**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

## 方法2: 使用启动脚本

### 1. 运行启动脚本
```bash
cd C:\Users\<USER>\Documents\augment-projects\zhidu\backend
python start_server.py
```

### 2. 查看启动信息
脚本会显示详细的启动信息和访问地址。

## 方法3: 测试连接

如果仍然无法访问，运行连接测试：
```bash
cd C:\Users\<USER>\Documents\augment-projects\zhidu\backend
python test_connection.py
```

## 常见问题解决

### 问题1: 端口被占用
如果看到端口8000被占用的错误：

1. 查看占用端口的进程：
```bash
netstat -ano | findstr :8000
```

2. 终止占用进程（替换PID为实际进程ID）：
```bash
taskkill /PID [进程ID] /F
```

### 问题2: 依赖包缺失
如果出现导入错误，安装依赖：
```bash
pip install fastapi uvicorn sqlalchemy bcrypt python-jose passlib python-multipart pydantic pydantic-settings requests python-dotenv
```

### 问题3: 数据库未初始化
如果是首次运行，初始化数据库：
```bash
python init_data.py
```

## 验证服务器运行

### 1. 检查服务器状态
在浏览器中访问：http://localhost:8000

应该看到类似这样的响应：
```json
{
  "message": "工厂制度文件智能生成与管理平台 API"
}
```

### 2. 检查API文档
访问：http://localhost:8000/docs

应该看到Swagger API文档界面。

### 3. 测试登录功能
- 默认用户名：`admin`
- 默认密码：`admin`

## 功能测试

### 1. 登录测试
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin"
```

### 2. 获取部门列表
```bash
curl -X GET "http://localhost:8000/api/v1/departments/"
```

### 3. 生成文档测试
```bash
curl -X POST "http://localhost:8000/api/v1/documents/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "采购控制程序",
    "departments": ["采购部", "质量部"],
    "standards": ["ISO 9001:2015 8.4"]
  }'
```

## 故障排除步骤

1. **确认Python环境**：运行 `python --version`
2. **确认依赖安装**：运行 `pip list | findstr fastapi`
3. **检查端口占用**：运行 `netstat -an | findstr :8000`
4. **查看错误日志**：检查命令行输出的错误信息
5. **重启服务器**：按 `Ctrl+C` 停止，然后重新运行

## 成功标志

当您看到以下内容时，说明服务器启动成功：

1. **命令行显示**：服务器启动信息（可能被缓冲，不一定立即显示）
2. **浏览器访问**：http://localhost:8000 返回JSON响应
3. **API文档**：http://localhost:8000/docs 显示Swagger界面
4. **端口监听**：`netstat -an | findstr :8000` 显示LISTENING状态

## 联系支持

如果按照以上步骤仍然无法解决问题，请提供：
1. 错误信息截图
2. 命令行输出
3. 浏览器错误信息

---

**提示**: 服务器启动后，请保持命令行窗口打开，关闭窗口会停止服务器。
