<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工厂制度文件智能生成与管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        .logo {
            font-size: 2.5em;
            color: #667eea;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .feature-card h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature-card p {
            color: #666;
            font-size: 0.9em;
        }
        
        .actions {
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            margin: 10px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            font-size: 1em;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .login-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .login-form {
            display: flex;
            gap: 10px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .login-form input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1em;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🏭 制度文件智能生成平台</div>
        <div class="subtitle">基于AI技术的工厂制度文件智能生成与管理系统</div>
        
        <div class="features">
            <div class="feature-card">
                <h3>🤖 AI智能生成</h3>
                <p>根据ISO标准和法规要求，自动生成专业的制度文件</p>
            </div>
            <div class="feature-card">
                <h3>📋 标准化管理</h3>
                <p>支持程序文件、作业指导书、管理方案等多种文件类型</p>
            </div>
            <div class="feature-card">
                <h3>🔄 流程可视化</h3>
                <p>可视化流程设计，清晰展示业务流程和职责分工</p>
            </div>
            <div class="feature-card">
                <h3>📊 知识库管理</h3>
                <p>集成ISO 9001等标准条款，智能推荐相关要求</p>
            </div>
        </div>
        
        <div class="login-section">
            <h3>🔐 系统登录</h3>
            <p>默认账号：admin / admin</p>
            <div class="login-form">
                <input type="text" id="username" placeholder="用户名" value="admin">
                <input type="password" id="password" placeholder="密码" value="admin">
                <button class="btn" onclick="login()">登录</button>
            </div>
            <div id="status" class="status"></div>
        </div>
        
        <div class="actions">
            <a href="/docs" class="btn" target="_blank">📚 API文档</a>
            <a href="/health" class="btn btn-secondary" target="_blank">🔍 系统状态</a>
            <button class="btn" onclick="generateDocument()">🚀 体验文档生成</button>
            <button class="btn" onclick="showDocumentForm()">📝 自定义文档</button>
        </div>

        <!-- 自定义文档生成表单 -->
        <div id="document-form" style="display: none; margin-top: 20px; background: #f8f9fa; padding: 20px; border-radius: 10px;">
            <h3>📝 自定义文档生成</h3>
            <div style="margin: 10px 0;">
                <label>文档标题：</label>
                <input type="text" id="doc-title" placeholder="例如：质量检验程序" style="width: 100%; padding: 8px; margin-top: 5px;">
            </div>
            <div style="margin: 10px 0;">
                <label>涉及部门（用逗号分隔）：</label>
                <input type="text" id="doc-departments" placeholder="例如：质量部,生产部,技术部" style="width: 100%; padding: 8px; margin-top: 5px;">
            </div>
            <div style="margin: 10px 0;">
                <label>关联标准（用逗号分隔）：</label>
                <input type="text" id="doc-standards" placeholder="例如：ISO 9001:2015 8.5,ISO 9001:2015 8.6" style="width: 100%; padding: 8px; margin-top: 5px;">
            </div>
            <div style="margin: 10px 0;">
                <label>过程输入：</label>
                <input type="text" id="doc-inputs" placeholder="例如：生产计划,技术文件" style="width: 100%; padding: 8px; margin-top: 5px;">
            </div>
            <div style="margin: 10px 0;">
                <label>过程输出：</label>
                <input type="text" id="doc-outputs" placeholder="例如：合格产品,质量记录" style="width: 100%; padding: 8px; margin-top: 5px;">
            </div>
            <div style="margin: 10px 0;">
                <label>关键绩效指标：</label>
                <input type="text" id="doc-kpis" placeholder="例如：合格率≥99%,及时率≥95%" style="width: 100%; padding: 8px; margin-top: 5px;">
            </div>
            <div style="margin: 20px 0;">
                <button class="btn" onclick="generateCustomDocument()">🚀 生成文档</button>
                <button class="btn btn-secondary" onclick="hideDocumentForm()">取消</button>
            </div>
        </div>
        
        <div id="demo-result" style="margin-top: 20px; display: none;">
            <h3>📄 生成的文档示例</h3>
            <div id="document-content" style="background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: left; margin-top: 10px;"></div>
        </div>
    </div>

    <script>
        // 登录功能
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const status = document.getElementById('status');
            
            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('token', data.access_token);
                    
                    status.className = 'status success';
                    status.style.display = 'block';
                    status.textContent = '✅ 登录成功！您现在可以使用所有功能。';
                } else {
                    throw new Error('登录失败');
                }
            } catch (error) {
                status.className = 'status error';
                status.style.display = 'block';
                status.textContent = '❌ 登录失败，请检查用户名和密码。';
            }
        }
        
        // 文档生成演示
        async function generateDocument() {
            const demoResult = document.getElementById('demo-result');
            const documentContent = document.getElementById('document-content');

            try {
                const response = await fetch('/api/v1/documents/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: '采购控制程序',
                        departments: ['采购部', '质量部'],
                        standards: ['ISO 9001:2015 8.4'],
                        process_data: {
                            inputs: '采购申请单',
                            outputs: '采购订单',
                            kpis: '及时率≥95%'
                        }
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    documentContent.innerHTML = `
                        <h4>${data.title}</h4>
                        <p><strong>涉及部门：</strong>${data.departments.join('、')}</p>
                        <p><strong>关联标准：</strong>${data.standards.join('、')}</p>
                        <div style="margin-top: 15px; white-space: pre-line; line-height: 1.6;">
                            ${data.content.substring(0, 500)}...
                        </div>
                        <p style="margin-top: 10px; color: #666; font-style: italic;">
                            ✨ 这是AI生成的文档内容示例，完整版本包含更多详细内容。
                        </p>
                    `;
                    demoResult.style.display = 'block';
                } else {
                    throw new Error('生成失败');
                }
            } catch (error) {
                alert('❌ 文档生成失败，请先登录或检查网络连接。');
            }
        }

        // 显示自定义文档表单
        function showDocumentForm() {
            document.getElementById('document-form').style.display = 'block';
        }

        // 隐藏自定义文档表单
        function hideDocumentForm() {
            document.getElementById('document-form').style.display = 'none';
        }

        // 生成自定义文档
        async function generateCustomDocument() {
            const title = document.getElementById('doc-title').value;
            const departments = document.getElementById('doc-departments').value.split(',').map(d => d.trim());
            const standards = document.getElementById('doc-standards').value.split(',').map(s => s.trim());
            const inputs = document.getElementById('doc-inputs').value;
            const outputs = document.getElementById('doc-outputs').value;
            const kpis = document.getElementById('doc-kpis').value;

            if (!title) {
                alert('请输入文档标题');
                return;
            }

            const demoResult = document.getElementById('demo-result');
            const documentContent = document.getElementById('document-content');

            try {
                const response = await fetch('/api/v1/documents/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title,
                        departments: departments,
                        standards: standards,
                        process_data: {
                            inputs: inputs,
                            outputs: outputs,
                            kpis: kpis
                        }
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    documentContent.innerHTML = `
                        <h4>${data.title}</h4>
                        <p><strong>涉及部门：</strong>${data.departments.join('、')}</p>
                        <p><strong>关联标准：</strong>${data.standards.join('、')}</p>
                        <div style="margin-top: 15px; white-space: pre-line; line-height: 1.6; max-height: 400px; overflow-y: auto;">
                            ${data.content}
                        </div>
                        <p style="margin-top: 10px; color: #666; font-style: italic;">
                            ✨ 这是根据您的要求生成的完整文档内容。
                        </p>
                    `;
                    demoResult.style.display = 'block';
                    hideDocumentForm();
                } else {
                    throw new Error('生成失败');
                }
            } catch (error) {
                alert('❌ 文档生成失败，请检查输入内容或网络连接。');
            }
        }
        
        // 页面加载时检查登录状态
        window.onload = function() {
            const token = localStorage.getItem('token');
            if (token) {
                const status = document.getElementById('status');
                status.className = 'status success';
                status.style.display = 'block';
                status.textContent = '✅ 您已登录，可以使用所有功能。';
            }
        };
    </script>
</body>
</html>
