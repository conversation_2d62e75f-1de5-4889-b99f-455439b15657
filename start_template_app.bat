@echo off
title 基于模板的AI制度文件生成器

echo ========================================
echo 🏭 基于模板的AI制度文件生成器
echo ========================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 安装依赖
echo 📦 检查依赖包...
pip install requests tkinter >nul 2>&1
echo ✅ 依赖包检查完成
echo.

REM 检查后端服务
echo 🔍 检查后端服务状态...
python -c "import requests; r = requests.get('http://localhost:8000/health'); print('✅ 后端服务正常' if r.status_code == 200 else '❌ 后端服务未启动')" 2>nul
if errorlevel 1 (
    echo.
    echo ⚠️  后端服务未启动，正在启动...
    cd backend
    start "后端API服务" cmd /k "echo 🔧 后端API服务 && echo 📍 地址: http://localhost:8000 && echo. && python main.py"
    cd ..
    echo ⏳ 等待后端服务启动...
    timeout /t 5 /nobreak >nul
)

echo.
echo 🖥️ 启动基于模板的AI制度文件生成器...
echo.
echo ========================================
echo 📋 使用说明:
echo 1. 在左侧输入或加载您的制度模板
echo 2. 选择适用的ISO标准
echo 3. 点击生成按钮，AI将生成符合ISO要求的制度文件
echo 4. 可以保存、复制或导出生成的文件
echo 5. 支持加载示例模板进行体验
echo ========================================
echo.

REM 启动模板生成器
python template_based_app.py

echo.
echo 🔚 应用程序已关闭
pause
