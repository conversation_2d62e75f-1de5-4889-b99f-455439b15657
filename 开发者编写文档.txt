
工厂制度文件智能生成与管理平台 V2.0 - 开发者技术实现方案

文档编号: GZ-DEV-001
版本: 1.0
目标: 指导“工厂制度文件智能生成与管理平台”的开发工作，明确系统架构、核心模块、数据模型及关键技术实现方案。

1. 系统架构概述

1.1 技术栈选型

前端: 推荐采用现代前端框架，如 Vue 3 或 React。使用组件库如 Element Plus / Ant Design 来快速构建UI。

AI编程提示: "Create a new Vue 3 project using Vite and install Element Plus."

后端: 推荐 Python (FastAPI / Flask) 或 C# (.NET 8)。

Python: 生态丰富，与AI/ML库（如requests, langchain）集成度高。

C#: 类型安全，性能优异，适合构建企业级健壮应用。

AI编程提示: "Create a new FastAPI project structure with a folder for routers, models, and services."

数据库: PostgreSQL 或 MySQL。对于初版或轻量级部署，可使用 SQLite。

ORM (对象关系映射):

Python: SQLAlchemy 或 Tortoise ORM (for FastAPI)。

C#: Entity Framework Core。

AI编程提示: "Define a SQLAlchemy model for a 'Document' table based on these fields..."

AI集成: 通过HTTP请求调用中转API，使用标准requests (Python) 或 HttpClient (C#) 库。

1.2 架构模式
采用 前后端分离 的 多层架构 (Layered Architecture)。

表现层 (Presentation Layer): 前端UI (Vue/React)。

应用层 (Application Layer / API): 后端API (FastAPI/.NET)，处理业务逻辑、身份验证和路由。

服务层 (Service Layer): 封装核心业务逻辑，如文件生成、流程处理、AI调用等。

数据访问层 (Data Access Layer): 使用ORM与数据库交互。

领域模型 (Domain Model): 定义核心业务对象（如Document, Process, User）。

2. 核心数据模型 (Database Schema)

这是系统的骨架，需要优先设计。使用ORM来定义。

User (用户表)

id (PK, int)

username (string, unique)

password_hash (string)

full_name (string)

position (string, 岗位)

department_id (FK -> Department)

role (enum: 'admin', 'manager', 'user')

employee_id (string, optional)

Department (部门表)

id (PK, int)

name (string)

parent_id (FK -> Department, nullable, for tree structure)

Document (文件主表)

id (PK, int)

doc_number (string, unique, e.g., "QP-01-001")

title (string)

version (string, e.g., "V1.0")

status (enum: 'draft', 'in_review', 'published', 'archived')

content (text, 存储富文本/Markdown)

creator_id (FK -> User)

owner_id (FK -> User, 过程所有者)

review_cycle_days (int, 评审周期)

next_review_date (date)

created_at, updated_at (timestamps)

parent_doc_id (FK -> Document, nullable, for versioning)

DocumentProcess (文件过程准则表)

id (PK, int)

document_id (FK -> Document, unique)

inputs (jsonb/text)

outputs (jsonb/text)

kpis (jsonb/text)

flowchart_data (jsonb/text, 存储BPMN.js的流程图数据)

KnowledgeClause (知识库条款表)

id (PK, int)

standard_name (string, e.g., "ISO 9001:2015")

clause_number (string)

title (string)

content (text)

keywords (array/text)

metadata (jsonb, 存储建议职责、风险点等元数据)

关联表 (Many-to-Many):

Document_Department_Link: document_id, department_id

Document_Clause_Link: document_id, clause_id

Document_Reference_Link: source_doc_id, referenced_doc_id (实现智能关联)

3. 模块化开发指南

3.1 后端 (Backend)

模块一：用户与权限模块 (auth)

功能: 用户注册、登录（JWT令牌）、密码管理、角色权限控制。

实现:

创建 /login, /register API端点。

使用中间件(Middleware)进行JWT验证，保护需要授权的API。

AI编程提示: "Generate FastAPI code for JWT authentication with login and a protected endpoint."

模块二：基础数据管理模块 (master_data)

功能: 增删改查部门、用户；处理组织架构、人员名单的导入/同步。

实现:

提供CRUD API для Department и User。

创建 /import/organization 端点，接收并解析JSON/CSV文件，批量更新Department和User表。

AI编程提示: "Write a Python function that takes a CSV file path, reads it using pandas, and populates the User and Department tables in SQLAlchemy."

模块三：AI服务模块 (ai_service)

功能: 封装对中转API的调用。

实现:

创建一个 AIService 类，如之前讨论的那样。

在系统配置中存储API URL和Key（加密存储）。

构建精巧的 system_prompt 和 user_prompt 模板库，用于不同场景（生成草稿、推荐条款等）。

AI编程提示: "Create a prompt template for generating a 'Purpose' section of a document, using placeholders for document title and inputs."

模块四：文件生成与管理模块 (document_service)

核心模块，逻辑复杂。

功能:

创建文件向导流程:

/documents/create_wizard: 分步处理前端发来的数据（基本信息、关联标准、流程图数据）。

AI生成草稿:

接收流程图数据（flowchart_data）。

解析flowchart_data，提取出关键活动、职责等信息。

构建完整的prompt，调用 AIService.generate_text()。

将返回的文本和流程图（可能需要前端渲染后截图或直接嵌入）组合成完整的content。

在 Document 表中创建一条新记录。

版本控制:

当修改published状态的文件时，不直接修改原记录。而是复制原记录，创建一条新status='draft'的记录，并设置其parent_doc_id为原记录ID。原记录状态变为archived。

智能关联分析:

当一个文件被保存时，解析其content，用正则表达式或关键词匹配查找其他文件的doc_number。

更新 Document_Reference_Link 表。

模块五：可视化流程建模器支持模块 (bpm_service)

功能: 保存和加载前端流程建模器的数据。

实现:

提供 /processes/{doc_id}/flowchart 的GET和POST端点，用于读写DocumentProcess.flowchart_data字段。

该字段存储BPMN.js或其他库生成的JSON/XML数据。

3.2 前端 (Frontend)

组件一：布局与导航 (Layout.vue)

功能: 主界面框架，包括顶部导航栏、侧边菜单、主内容区。

实现: 使用Vue Router管理页面路由。

组件二：可视化流程建模器 (BPMNEditor.vue)

功能: 集成一个流程图库，如 BPMN.js。

实现:

初始化BPMN.js画布。

提供从后端加载流程图数据的功能。

提供保存流程图数据到后端的功能。

在右侧属性面板中，动态加载部门、人员列表，供用户选择分配职责。

AI编程提示: "Show me a basic example of how to integrate BPMN.js into a Vue 3 component."

组件三：文件编辑器 (DocumentEditor.vue)

功能: 富文本编辑器，用于审阅和微调AI生成的草稿。

实现: 集成 TinyMCE、CKEditor 或更轻量的 Tiptap。

AI编程提示: "How to set up Tiptap editor in a Vue 3 component with basic formatting options?"

组件四：文件库/仪表盘 (DocumentLibrary.vue, Dashboard.vue)

功能: 使用表格和图表组件（如 ECharts）展示文件列表和体系运行数据。

实现:

调用后端API获取数据。

实现分页、搜索、筛选功能。

配置ECharts的option来渲染图表。

4. 开发与部署工作流

环境搭建:

本地安装好所选技术栈的开发环境（Node.js, Python/NET, DB）。

初始化前后端项目仓库。

数据库先行:

使用ORM定义好所有数据模型。

执行数据库迁移 (migration)，生成数据库表结构。

后端API开发:

从最基础的auth和master_data模块开始。

使用 Postman 或 Insomnia 等工具测试API接口，确保逻辑正确。

前端组件开发:

并行开发UI组件，先用静态假数据(mock data)搭建界面。

待后端API可用后，替换为真实API调用。

核心功能联调:

重点联调“文件生成向导”流程，确保从前端流程图数据到后端AI调用再到前端草稿展示的通路顺畅。

部署:

后端: 使用 Docker 进行容器化部署。

前端: 构建成静态文件，可通过 Nginx 或云存储服务托管。

CI/CD: 建立自动化构建和部署流水线（如使用 GitHub Actions）。

这份开发者文档为你和你的AI搭档提供了一张详尽的“施工图”。现在，你可以拿起“锤子”（VS Code），对着这张图纸，一块一块地把这个强大的平台构建起来。