from pydantic import BaseModel
from typing import Optional
from datetime import datetime, date
from app.models.document import DocumentStatus


class DocumentBase(BaseModel):
    title: str
    version: str = "V1.0"
    status: DocumentStatus = DocumentStatus.DRAFT
    content: Optional[str] = None
    creator_id: int
    owner_id: Optional[int] = None
    review_cycle_days: int = 365
    next_review_date: Optional[date] = None
    parent_doc_id: Optional[int] = None


class DocumentCreate(DocumentBase):
    pass


class DocumentUpdate(BaseModel):
    title: Optional[str] = None
    version: Optional[str] = None
    status: Optional[DocumentStatus] = None
    content: Optional[str] = None
    owner_id: Optional[int] = None
    review_cycle_days: Optional[int] = None
    next_review_date: Optional[date] = None


class DocumentResponse(DocumentBase):
    id: int
    doc_number: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class DocumentProcessBase(BaseModel):
    document_id: int
    inputs: Optional[str] = None
    outputs: Optional[str] = None
    kpis: Optional[str] = None
    flowchart_data: Optional[str] = None


class DocumentProcessCreate(DocumentProcessBase):
    pass


class DocumentProcessUpdate(BaseModel):
    inputs: Optional[str] = None
    outputs: Optional[str] = None
    kpis: Optional[str] = None
    flowchart_data: Optional[str] = None


class DocumentProcessResponse(DocumentProcessBase):
    id: int

    class Config:
        from_attributes = True
