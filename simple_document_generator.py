#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版AI制度文件生成器
稳定可靠，无复杂依赖
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from datetime import datetime
import json

class SimpleDocumentGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("🏛️ AI制度文件生成器 - 简化版")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # 创建界面
        self.create_interface()
        
        # 初始化状态
        self.update_status("系统已就绪")
    
    def create_interface(self):
        """创建用户界面"""
        # 标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🏛️ AI制度文件生成器", 
                              font=('Microsoft YaHei', 16, 'bold'), 
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # 主容器
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 左侧输入区域
        left_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        left_frame.pack(side='left', fill='y', padx=(0, 5))
        
        # 右侧结果区域
        right_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        # 创建左侧内容
        self.create_input_panel(left_frame)
        
        # 创建右侧内容
        self.create_result_panel(right_frame)
        
        # 状态栏
        self.status_bar = tk.Label(self.root, text="就绪", relief=tk.SUNKEN, anchor=tk.W,
                                  font=('Microsoft YaHei', 9), bg='#e9ecef')
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_input_panel(self, parent):
        """创建输入面板"""
        # 基本信息
        info_frame = tk.LabelFrame(parent, text="📋 基本信息", font=('Microsoft YaHei', 11, 'bold'))
        info_frame.pack(fill='x', padx=10, pady=10)
        
        # 组织名称
        tk.Label(info_frame, text="组织名称:", font=('Microsoft YaHei', 10)).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.org_name_entry = tk.Entry(info_frame, width=25, font=('Microsoft YaHei', 10))
        self.org_name_entry.grid(row=0, column=1, padx=5, pady=5)
        self.org_name_entry.insert(0, "某制造企业有限公司")
        
        # 组织类型
        tk.Label(info_frame, text="组织类型:", font=('Microsoft YaHei', 10)).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.org_type_var = tk.StringVar(value="企业")
        org_type_combo = ttk.Combobox(info_frame, textvariable=self.org_type_var, width=23, font=('Microsoft YaHei', 10))
        org_type_combo['values'] = ('企业', '政府机构', '非营利组织', '事业单位')
        org_type_combo.grid(row=1, column=1, padx=5, pady=5)
        
        # 行业类型
        tk.Label(info_frame, text="行业类型:", font=('Microsoft YaHei', 10)).grid(row=2, column=0, sticky='w', padx=5, pady=5)
        self.industry_var = tk.StringVar(value="制造业")
        industry_combo = ttk.Combobox(info_frame, textvariable=self.industry_var, width=23, font=('Microsoft YaHei', 10))
        industry_combo['values'] = ('制造业', '金融业', '医疗健康', '教育', '科技', '零售', '建筑', '物流')
        industry_combo.grid(row=2, column=1, padx=5, pady=5)
        
        # 文档类型
        tk.Label(info_frame, text="文档类型:", font=('Microsoft YaHei', 10)).grid(row=3, column=0, sticky='w', padx=5, pady=5)
        self.doc_type_var = tk.StringVar(value="企业规章")
        doc_type_combo = ttk.Combobox(info_frame, textvariable=self.doc_type_var, width=23, font=('Microsoft YaHei', 10))
        doc_type_combo['values'] = ('企业规章', '政策文件', '操作流程', '质量标准', '管理手册', '指导原则')
        doc_type_combo.grid(row=3, column=1, padx=5, pady=5)
        
        # 生成参数
        params_frame = tk.LabelFrame(parent, text="🎯 生成参数", font=('Microsoft YaHei', 11, 'bold'))
        params_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 文档框架
        tk.Label(params_frame, text="文档框架描述:", font=('Microsoft YaHei', 10)).pack(anchor='w', padx=5, pady=(5,0))
        self.framework_text = scrolledtext.ScrolledText(params_frame, height=6, font=('Microsoft YaHei', 10))
        self.framework_text.pack(fill='x', padx=5, pady=5)
        self.framework_text.insert('1.0', '建立完善的质量管理制度，确保产品质量符合标准要求，涵盖质量策划、质量控制、质量保证和质量改进全过程')
        
        # 关键词
        tk.Label(params_frame, text="关键词 (用逗号分隔):", font=('Microsoft YaHei', 10)).pack(anchor='w', padx=5, pady=(5,0))
        self.keywords_entry = tk.Entry(params_frame, font=('Microsoft YaHei', 10))
        self.keywords_entry.pack(fill='x', padx=5, pady=5)
        self.keywords_entry.insert(0, '质量管理, 产品检验, 不合格品控制, 持续改进')
        
        # 快速模板
        template_frame = tk.LabelFrame(parent, text="⚡ 快速模板", font=('Microsoft YaHei', 11, 'bold'))
        template_frame.pack(fill='x', padx=10, pady=10)
        
        templates = [
            ("📋 质量管理制度", self.load_quality_template),
            ("📜 采购控制程序", self.load_purchase_template),
            ("🔄 生产管理流程", self.load_production_template),
            ("📊 安全管理标准", self.load_safety_template)
        ]
        
        for text, command in templates:
            btn = tk.Button(template_frame, text=text, command=command,
                           bg='#f8f9fa', font=('Microsoft YaHei', 9), width=20)
            btn.pack(fill='x', padx=5, pady=2)
        
        # 生成按钮
        generate_btn = tk.Button(parent, text="🚀 开始AI智能生成", 
                               command=self.generate_document,
                               bg='#28a745', fg='white', 
                               font=('Microsoft YaHei', 12, 'bold'),
                               height=2)
        generate_btn.pack(fill='x', padx=10, pady=20)
    
    def create_result_panel(self, parent):
        """创建结果面板"""
        # 结果标题
        result_title = tk.Label(parent, text="📄 生成的制度文件", 
                               font=('Microsoft YaHei', 12, 'bold'))
        result_title.pack(pady=10)
        
        # 文档信息
        self.doc_info_frame = tk.Frame(parent, bg='#e3f2fd', relief='sunken', bd=1)
        self.doc_info_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        self.doc_info_label = tk.Label(self.doc_info_frame, text="等待生成文档...", 
                                      font=('Microsoft YaHei', 9), bg='#e3f2fd')
        self.doc_info_label.pack(pady=8)
        
        # 文档内容
        self.result_text = scrolledtext.ScrolledText(parent, wrap=tk.WORD, 
                                                    font=('Microsoft YaHei', 10),
                                                    bg='#fafafa', fg='#333')
        self.result_text.pack(fill='both', expand=True, padx=10, pady=(0, 10))
        
        # 操作按钮
        button_frame = tk.Frame(parent)
        button_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        save_btn = tk.Button(button_frame, text="💾 保存文档", command=self.save_document,
                           bg='#007bff', fg='white', font=('Microsoft YaHei', 10))
        save_btn.pack(side='left', padx=(0, 5))
        
        copy_btn = tk.Button(button_frame, text="📋 复制内容", command=self.copy_content,
                           bg='#6c757d', fg='white', font=('Microsoft YaHei', 10))
        copy_btn.pack(side='left', padx=5)
        
        clear_btn = tk.Button(button_frame, text="🗑️ 清空", command=self.clear_content,
                            bg='#dc3545', fg='white', font=('Microsoft YaHei', 10))
        clear_btn.pack(side='left', padx=5)
        
        check_btn = tk.Button(button_frame, text="✅ 合规检查", command=self.compliance_check,
                            bg='#28a745', fg='white', font=('Microsoft YaHei', 10))
        check_btn.pack(side='right')
    
    def generate_document(self):
        """生成文档"""
        # 获取输入参数
        org_name = self.org_name_entry.get().strip()
        org_type = self.org_type_var.get()
        industry = self.industry_var.get()
        doc_type = self.doc_type_var.get()
        framework = self.framework_text.get('1.0', tk.END).strip()
        keywords_str = self.keywords_entry.get().strip()
        
        if not org_name or not framework or not keywords_str:
            messagebox.showerror("错误", "请填写完整的基本信息和生成参数")
            return
        
        keywords = [k.strip() for k in keywords_str.split(',') if k.strip()]
        
        self.update_status("🔄 正在生成制度文件...")
        
        # 生成文档内容
        content = self.create_document_content(org_name, org_type, industry, doc_type, framework, keywords)
        
        # 更新结果显示
        self.result_text.delete('1.0', tk.END)
        self.result_text.insert('1.0', content)
        
        # 更新文档信息
        info_text = f"📄 {doc_type}\n🏢 {org_name}\n📅 {datetime.now().strftime('%Y年%m月%d日 %H:%M')}\n📊 {len(content)} 字符"
        self.doc_info_label.config(text=info_text)
        
        self.update_status("✅ 文档生成完成")
    
    def create_document_content(self, org_name, org_type, industry, doc_type, framework, keywords):
        """创建文档内容"""
        current_time = datetime.now()
        
        content = f"""{doc_type}

文件编号：[待分配]
版本号：V1.0
生效日期：{current_time.strftime('%Y年%m月%d日')}
制定单位：{org_name}
适用范围：全{org_type}

第一章 总则

第一条 目的和依据
{framework}

根据《中华人民共和国公司法》、《中华人民共和国劳动法》等相关法律法规，结合本{org_type}实际情况，制定本制度。

第二条 适用范围
本制度适用于{org_name}的相关业务活动，涵盖{', '.join(keywords[:3])}等关键领域。

第三条 基本原则
（一）合法合规原则：严格遵守国家法律法规和行业标准；
（二）科学管理原则：建立科学、规范的管理体系；
（三）持续改进原则：不断优化和完善管理流程；
（四）责任明确原则：明确各级人员的职责和权限。

第二章 组织架构与职责

第四条 组织架构
建立健全{', '.join(keywords)}管理体系，实行分级管理：
（一）决策层：负责重大决策和政策制定；
（二）管理层：负责具体实施和监督管理；
（三）执行层：负责具体操作和执行。

第五条 职责分工
（一）主要负责人：对制度执行负总责，决策重大事项；
（二）分管领导：负责分管领域的制度执行和监督；
（三）部门负责人：负责本部门制度的具体实施；
（四）工作人员：严格按照制度要求履行职责。

第三章 管理要求

第六条 基本要求
围绕{', '.join(keywords)}等核心要素，建立完善的管理制度：
（一）严格遵守国家法律法规和行业标准；
（二）建立健全管理制度和操作规程；
（三）明确工作流程和操作规范；
（四）加强人员培训和能力建设；
（五）建立记录档案和追溯机制。

第七条 操作流程
（一）计划阶段：制定工作计划，明确目标任务；
（二）实施阶段：按照规定程序组织实施；
（三）检查阶段：定期检查执行情况和效果；
（四）改进阶段：总结经验教训，持续改进完善。

第八条 质量控制
（一）建立质量管理体系，明确质量标准和要求；
（二）实施过程控制，确保各环节质量；
（三）开展质量检查，及时发现和纠正问题；
（四）持续改进质量管理，提升整体水平。

第四章 监督检查

第九条 内部监督
建立内部监督检查机制，定期对制度执行情况进行检查评估：
（一）制定监督检查计划和标准；
（二）组织开展定期和不定期检查；
（三）及时发现和纠正存在的问题；
（四）建立问题整改和跟踪机制。

第十条 外部监督
接受上级部门和相关监管机构的监督检查：
（一）积极配合外部监督检查工作；
（二）及时整改发现的问题和不足；
（三）建立外部监督反馈机制。

第五章 奖惩措施

第十一条 奖励机制
对严格执行制度、成效显著的部门和个人给予表彰奖励：
（一）设立专项奖励基金；
（二）建立多层次奖励体系；
（三）定期开展评优评先活动。

第十二条 责任追究
对违反制度规定的行为，依据情节轻重给予相应处理：
（一）轻微违规：批评教育、警告；
（二）一般违规：通报批评、扣减绩效；
（三）严重违规：调离岗位、解除合同。

第六章 附则

第十三条 制度解释
本制度由相关职能部门负责解释和修订。

第十四条 生效时间
本制度自发布之日起施行，原相关制度同时废止。

第十五条 修订程序
本制度的修订须经过充分论证和审批程序。

附件：
1. 相关记录表格
2. 操作流程图
3. 法律法规清单
4. 应急处置预案

制定：[制定人]
审核：[审核人]
批准：[批准人]

{current_time.strftime('%Y年%m月%d日')}"""
        
        return content
    
    def load_quality_template(self):
        """加载质量管理模板"""
        self.org_type_var.set("企业")
        self.industry_var.set("制造业")
        self.doc_type_var.set("企业规章")
        self.framework_text.delete('1.0', tk.END)
        self.framework_text.insert('1.0', '建立完善的质量管理制度，确保产品质量符合ISO 9001标准要求，涵盖质量策划、质量控制、质量保证和质量改进全过程')
        self.keywords_entry.delete(0, tk.END)
        self.keywords_entry.insert(0, '质量管理, 产品检验, 不合格品控制, 持续改进, 客户满意')
        self.update_status("已加载质量管理制度模板")
    
    def load_purchase_template(self):
        """加载采购控制模板"""
        self.org_type_var.set("企业")
        self.industry_var.set("制造业")
        self.doc_type_var.set("操作流程")
        self.framework_text.delete('1.0', tk.END)
        self.framework_text.insert('1.0', '规范采购控制流程，确保采购的产品和服务满足规定要求，建立供应商评估和管理体系')
        self.keywords_entry.delete(0, tk.END)
        self.keywords_entry.insert(0, '采购控制, 供应商管理, 采购评审, 进料检验, 合同管理')
        self.update_status("已加载采购控制程序模板")
    
    def load_production_template(self):
        """加载生产管理模板"""
        self.org_type_var.set("企业")
        self.industry_var.set("制造业")
        self.doc_type_var.set("管理手册")
        self.framework_text.delete('1.0', tk.END)
        self.framework_text.insert('1.0', '建立标准化的生产管理流程，确保生产过程受控，产品质量稳定，生产效率持续提升')
        self.keywords_entry.delete(0, tk.END)
        self.keywords_entry.insert(0, '生产管理, 工艺控制, 设备管理, 现场管理, 安全生产')
        self.update_status("已加载生产管理流程模板")
    
    def load_safety_template(self):
        """加载安全管理模板"""
        self.org_type_var.set("企业")
        self.industry_var.set("制造业")
        self.doc_type_var.set("质量标准")
        self.framework_text.delete('1.0', tk.END)
        self.framework_text.insert('1.0', '建立完善的安全管理标准，确保生产安全，预防安全事故，保障员工生命财产安全')
        self.keywords_entry.delete(0, tk.END)
        self.keywords_entry.insert(0, '安全管理, 安全生产, 事故预防, 应急处置, 安全培训')
        self.update_status("已加载安全管理标准模板")
    
    def save_document(self):
        """保存文档"""
        content = self.result_text.get('1.0', tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "没有可保存的文档内容")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存制度文件",
            defaultextension=".txt",
            filetypes=[
                ("文本文件", "*.txt"),
                ("Word文档", "*.docx"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.update_status(f"✅ 文档已保存: {file_path}")
                messagebox.showinfo("成功", f"文档已保存到:\n{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
    
    def copy_content(self):
        """复制内容"""
        content = self.result_text.get('1.0', tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "没有可复制的内容")
            return
        
        self.root.clipboard_clear()
        self.root.clipboard_append(content)
        self.update_status("📋 内容已复制到剪贴板")
        messagebox.showinfo("成功", "文档内容已复制到剪贴板")
    
    def clear_content(self):
        """清空内容"""
        self.result_text.delete('1.0', tk.END)
        self.doc_info_label.config(text="等待生成文档...")
        self.update_status("🗑️ 内容已清空")
    
    def compliance_check(self):
        """合规性检查"""
        content = self.result_text.get('1.0', tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "没有可检查的内容")
            return
        
        # 简单的合规性检查
        issues = []
        required_sections = ["总则", "职责", "要求", "监督", "附则"]
        
        for section in required_sections:
            if section not in content:
                issues.append(f"缺少必要章节：{section}")
        
        if "法律法规" not in content:
            issues.append("建议添加法律法规依据")
        
        if "生效" not in content and "施行" not in content:
            issues.append("缺少生效条款")
        
        if not issues:
            messagebox.showinfo("合规性检查", "✅ 文档结构完整，符合基本规范要求")
        else:
            issue_text = "\n".join(f"• {issue}" for issue in issues)
            messagebox.showwarning("合规性检查", f"发现以下问题:\n\n{issue_text}\n\n建议根据实际情况进行调整")
        
        self.update_status("✅ 合规性检查完成")
    
    def update_status(self, message):
        """更新状态栏"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_bar.config(text=f"[{timestamp}] {message}")
        self.root.update()

def main():
    root = tk.Tk()
    app = SimpleDocumentGenerator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
