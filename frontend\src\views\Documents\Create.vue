<template>
  <div class="create-document">
    <div class="page-header">
      <h1>新建文件</h1>
      <p>通过AI智能生成制度文件</p>
    </div>

    <el-card class="wizard-card">
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="基础信息" description="填写文件基本信息"></el-step>
        <el-step title="定义过程" description="设置过程要素"></el-step>
        <el-step title="生成预览" description="AI生成文档内容"></el-step>
      </el-steps>

      <div class="step-content">
        <!-- 步骤1: 基础信息 -->
        <div v-if="currentStep === 0" class="step-form">
          <el-form :model="formData" :rules="rules" ref="basicFormRef" label-width="120px">
            <el-form-item label="文件名称" prop="title">
              <el-input
                v-model="formData.title"
                placeholder="例如：采购控制程序"
                @input="onTitleChange"
              />
            </el-form-item>

            <el-form-item label="涉及部门" prop="departments">
              <el-select
                v-model="formData.departments"
                multiple
                placeholder="请选择涉及的部门"
                style="width: 100%"
              >
                <el-option
                  v-for="dept in departmentOptions"
                  :key="dept.value"
                  :label="dept.label"
                  :value="dept.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="关联标准/法规">
              <div class="standards-section">
                <div v-if="recommendedStandards.length > 0" class="recommended-standards">
                  <p class="section-title">💡 AI 智能推荐:</p>
                  <div class="standard-tags">
                    <el-tag
                      v-for="standard in recommendedStandards"
                      :key="standard.clause"
                      type="info"
                      class="recommended-tag"
                      @click="addStandard(standard)"
                    >
                      {{ standard.standard }}: {{ standard.clause }}
                    </el-tag>
                  </div>
                </div>

                <el-input
                  v-model="standardSearch"
                  placeholder="手动搜索或添加其他标准..."
                  @keyup.enter="addCustomStandard"
                />

                <div v-if="formData.standards.length > 0" class="selected-standards">
                  <p class="section-title">已选择的标准:</p>
                  <el-tag
                    v-for="(standard, index) in formData.standards"
                    :key="index"
                    closable
                    @close="removeStandard(index)"
                  >
                    {{ standard }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 步骤2: 定义过程 -->
        <div v-if="currentStep === 1" class="step-form">
          <el-form :model="processData" label-width="120px">
            <el-form-item label="过程输入">
              <el-input
                v-model="processData.inputs"
                type="textarea"
                :rows="3"
                placeholder="例如：物料需求申请单、采购计划"
              />
            </el-form-item>

            <el-form-item label="过程输出">
              <el-input
                v-model="processData.outputs"
                type="textarea"
                :rows="3"
                placeholder="例如：采购订单、进料检验报告"
              />
            </el-form-item>

            <el-form-item label="关键绩效指标">
              <el-input
                v-model="processData.kpis"
                type="textarea"
                :rows="3"
                placeholder="例如：采购及时率≥95%、合格率≥98%"
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- 步骤3: 生成预览 -->
        <div v-if="currentStep === 2" class="step-preview">
          <div v-if="generating" class="generating">
            <el-icon class="is-loading"><Loading /></el-icon>
            <p>AI正在生成文档内容，请稍候...</p>
          </div>

          <div v-else-if="generatedContent" class="preview-content">
            <div class="preview-header">
              <h3>{{ formData.title }}</h3>
              <el-button type="primary" @click="saveDocument">保存文档</el-button>
            </div>
            <div class="content-preview" v-html="formatContent(generatedContent)"></div>
          </div>

          <div v-else class="preview-empty">
            <p>点击"生成文档"开始AI生成</p>
            <el-button type="primary" @click="generateDocument" :loading="generating">
              生成文档
            </el-button>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="wizard-actions">
        <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
        <el-button
          v-if="currentStep < 2"
          type="primary"
          @click="nextStep"
          :disabled="!canProceed"
        >
          下一步
        </el-button>
        <el-button
          v-if="currentStep === 2 && !generatedContent"
          type="primary"
          @click="generateDocument"
          :loading="generating"
        >
          生成文档
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

const currentStep = ref(0)
const generating = ref(false)
const generatedContent = ref('')
const standardSearch = ref('')
const recommendedStandards = ref([])

const basicFormRef = ref()

const formData = reactive({
  title: '',
  departments: [],
  standards: []
})

const processData = reactive({
  inputs: '',
  outputs: '',
  kpis: ''
})

const rules = {
  title: [
    { required: true, message: '请输入文件名称', trigger: 'blur' }
  ],
  departments: [
    { required: true, message: '请选择涉及部门', trigger: 'change' }
  ]
}

const departmentOptions = [
  { label: '总经理办公室', value: '总经理办公室' },
  { label: '质量部', value: '质量部' },
  { label: '采购部', value: '采购部' },
  { label: '生产部', value: '生产部' },
  { label: '仓库', value: '仓库' },
  { label: '人事部', value: '人事部' }
]

const canProceed = computed(() => {
  if (currentStep.value === 0) {
    return formData.title && formData.departments.length > 0
  }
  return true
})

// 监听标题变化，获取AI推荐
watch(() => formData.title, (newTitle) => {
  if (newTitle) {
    getRecommendedStandards(newTitle)
  }
})

const onTitleChange = () => {
  // 标题变化时的处理逻辑
}

const getRecommendedStandards = (title) => {
  // 模拟AI推荐标准
  const recommendations = []

  if (title.includes('采购')) {
    recommendations.push(
      { standard: 'ISO 9001:2015', clause: '8.4', title: '外部提供的过程、产品和服务的控制' },
      { standard: 'ISO 9001:2015', clause: '8.4.1', title: '总则' }
    )
  }

  if (title.includes('质量')) {
    recommendations.push(
      { standard: 'ISO 9001:2015', clause: '7.1.5', title: '监视和测量资源' }
    )
  }

  recommendedStandards.value = recommendations
}

const addStandard = (standard) => {
  const standardText = `${standard.standard}: ${standard.clause}`
  if (!formData.standards.includes(standardText)) {
    formData.standards.push(standardText)
  }
}

const addCustomStandard = () => {
  if (standardSearch.value && !formData.standards.includes(standardSearch.value)) {
    formData.standards.push(standardSearch.value)
    standardSearch.value = ''
  }
}

const removeStandard = (index) => {
  formData.standards.splice(index, 1)
}

const nextStep = async () => {
  if (currentStep.value === 0) {
    const valid = await basicFormRef.value.validate().catch(() => false)
    if (!valid) return
  }

  if (currentStep.value < 2) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const generateDocument = async () => {
  generating.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟生成的内容
    generatedContent.value = `
# ${formData.title}

## 1. 目的
本程序旨在规范${formData.title.replace('程序', '').replace('控制', '')}相关活动，确保符合质量管理体系要求。

## 2. 范围
本程序适用于${formData.departments.join('、')}的相关工作活动。

## 3. 职责
${formData.departments.map(dept => `- ${dept}：负责${formData.title.replace('程序', '').replace('控制', '')}相关工作的执行和监督`).join('\n')}

## 4. 工作程序
4.1 准备阶段
- 确定工作要求和标准
- 准备必要的资源和工具

4.2 执行阶段
- 按照既定程序执行工作
- 记录执行过程和结果

4.3 检查阶段
- 对执行结果进行检查和验证
- 识别和处理不符合项

4.4 改进阶段
- 分析执行过程中的问题
- 制定改进措施

## 5. 相关记录
- 工作记录表
- 检查记录表
- 不符合项处理记录

---
*本文档由AI智能生成，请根据实际情况进行调整和完善。*
    `

    ElMessage.success('文档生成成功！')
  } catch (error) {
    ElMessage.error('文档生成失败，请重试')
  } finally {
    generating.value = false
  }
}

const formatContent = (content) => {
  // 简单的Markdown转HTML
  return content
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^\- (.*$)/gm, '<li>$1</li>')
    .replace(/\n/g, '<br>')
}

const saveDocument = () => {
  ElMessage.success('文档保存成功！')
  // 这里可以调用保存API
}
</script>

<style scoped>
.create-document {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  color: #909399;
}

.wizard-card {
  min-height: 600px;
}

.step-content {
  margin: 40px 0;
  min-height: 400px;
}

.step-form {
  max-width: 600px;
  margin: 0 auto;
}

.standards-section {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.standard-tags {
  margin-bottom: 15px;
}

.recommended-tag {
  margin-right: 10px;
  margin-bottom: 5px;
  cursor: pointer;
}

.recommended-tag:hover {
  background-color: #409EFF;
  color: white;
}

.selected-standards {
  margin-top: 15px;
}

.selected-standards .el-tag {
  margin-right: 10px;
  margin-bottom: 5px;
}

.step-preview {
  text-align: center;
}

.generating {
  padding: 60px 0;
}

.generating .el-icon {
  font-size: 40px;
  color: #409EFF;
  margin-bottom: 20px;
}

.preview-content {
  text-align: left;
  max-width: 800px;
  margin: 0 auto;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.content-preview {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 4px;
  line-height: 1.6;
}

.content-preview h1 {
  color: #303133;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 10px;
}

.content-preview h2 {
  color: #606266;
  margin-top: 30px;
  margin-bottom: 15px;
}

.content-preview h3 {
  color: #909399;
  margin-top: 20px;
  margin-bottom: 10px;
}

.preview-empty {
  padding: 60px 0;
}

.wizard-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.wizard-actions .el-button {
  margin: 0 10px;
}
</style>
