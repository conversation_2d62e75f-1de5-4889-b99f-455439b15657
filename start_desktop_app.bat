@echo off
echo ========================================
echo 工厂制度文件智能生成平台 - 桌面版
echo ========================================
echo.

echo 正在启动桌面应用程序...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

REM 检查并安装依赖
echo 检查依赖包...
pip install requests >nul 2>&1

REM 启动桌面应用
echo 启动桌面应用程序...
python desktop_app.py

echo.
echo 应用程序已关闭
pause
