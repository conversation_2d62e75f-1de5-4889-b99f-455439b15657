#!/usr/bin/env python3
"""
简单的测试服务器
"""
from fastapi import FastAPI
import uvicorn

# 创建简单的FastAPI应用
app = FastAPI(
    title="工厂制度文件智能生成与管理平台",
    description="测试服务器",
    version="1.0.0"
)

@app.get("/")
def read_root():
    return {
        "message": "🎉 工厂制度文件智能生成与管理平台",
        "status": "运行正常",
        "version": "1.0.0"
    }

@app.get("/health")
def health_check():
    return {"status": "healthy", "message": "服务器运行正常"}

@app.get("/test")
def test_endpoint():
    return {
        "test": "success",
        "message": "测试端点工作正常",
        "features": [
            "用户认证",
            "文档管理", 
            "AI生成",
            "知识库"
        ]
    }

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 启动简单测试服务器")
    print("=" * 60)
    print("📍 地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🧪 测试端点: http://localhost:8000/test")
    print("=" * 60)
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
