"""
初始化数据库数据
"""
from app.core.database import SessionLocal, engine
from app.models import Base, User, Department, KnowledgeClause
from app.core.security import get_password_hash


def init_database():
    """初始化数据库"""
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    
    try:
        # 创建部门
        departments = [
            Department(id=1, name="总经理办公室"),
            Department(id=2, name="质量部", parent_id=1),
            Department(id=3, name="采购部", parent_id=1),
            Department(id=4, name="生产部", parent_id=1),
            Department(id=5, name="仓库", parent_id=1),
            Department(id=6, name="人事部", parent_id=1),
        ]
        
        for dept in departments:
            existing = db.query(Department).filter(Department.id == dept.id).first()
            if not existing:
                db.add(dept)
        
        # 创建用户
        users = [
            User(
                username="admin",
                password_hash=get_password_hash("admin"),
                full_name="系统管理员",
                position="管理员",
                role="admin",
                department_id=1
            ),
            User(
                username="quality_manager",
                password_hash=get_password_hash("123456"),
                full_name="质量经理",
                position="质量经理",
                role="manager",
                department_id=2
            ),
            User(
                username="purchase_manager",
                password_hash=get_password_hash("123456"),
                full_name="采购经理",
                position="采购经理",
                role="manager",
                department_id=3
            ),
        ]
        
        for user in users:
            existing = db.query(User).filter(User.username == user.username).first()
            if not existing:
                db.add(user)
        
        # 创建知识库条款
        knowledge_clauses = [
            KnowledgeClause(
                standard_name="ISO 9001:2015",
                clause_number="8.4",
                title="外部提供的过程、产品和服务的控制",
                content="组织应确保外部提供的过程、产品和服务符合要求。",
                keywords="采购,供应商,外部提供",
                meta_data='{"建议职责": ["采购部", "质量部"], "风险点": ["供应商选择不当", "质量控制不足"]}'
            ),
            KnowledgeClause(
                standard_name="ISO 9001:2015",
                clause_number="8.4.1",
                title="总则",
                content="组织应确保外部提供的过程、产品和服务不会对组织稳定地向顾客交付合格产品和服务的能力产生不利影响。",
                keywords="采购控制,供应商管理",
                meta_data='{"建议职责": ["采购部"], "风险点": ["供应链中断", "质量问题"]}'
            ),
            KnowledgeClause(
                standard_name="ISO 9001:2015",
                clause_number="7.1.5",
                title="监视和测量资源",
                content="当利用监视或测量来验证产品和服务符合要求时，组织应确定并提供所需的资源。",
                keywords="监视,测量,资源",
                meta_data='{"建议职责": ["质量部", "生产部"], "风险点": ["设备校准失效", "测量不准确"]}'
            ),
        ]
        
        for clause in knowledge_clauses:
            existing = db.query(KnowledgeClause).filter(
                KnowledgeClause.standard_name == clause.standard_name,
                KnowledgeClause.clause_number == clause.clause_number
            ).first()
            if not existing:
                db.add(clause)
        
        db.commit()
        print("数据库初始化完成！")
        print("默认管理员账号: admin / admin")
        
    except Exception as e:
        print(f"初始化失败: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    init_database()
