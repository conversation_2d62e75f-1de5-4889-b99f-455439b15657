from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.core.database import get_db
from app.models.knowledge import KnowledgeClause
from app.schemas.knowledge import KnowledgeClauseResponse, KnowledgeClauseCreate

router = APIRouter()

@router.get("/", response_model=List[KnowledgeClauseResponse])
def get_knowledge_clauses(
    standard_name: Optional[str] = Query(None, description="标准名称"),
    keywords: Optional[str] = Query(None, description="关键词搜索"),
    db: Session = Depends(get_db)
):
    """获取知识库条款列表"""
    query = db.query(KnowledgeClause)

    if standard_name:
        query = query.filter(KnowledgeClause.standard_name.contains(standard_name))

    if keywords:
        query = query.filter(KnowledgeClause.keywords.contains(keywords))

    clauses = query.all()
    return clauses

@router.get("/standards")
def get_standards(db: Session = Depends(get_db)):
    """获取所有标准名称"""
    standards = db.query(KnowledgeClause.standard_name).distinct().all()
    return [standard[0] for standard in standards]

@router.post("/", response_model=KnowledgeClauseResponse)
def create_knowledge_clause(clause: KnowledgeClauseCreate, db: Session = Depends(get_db)):
    """创建知识库条款"""
    db_clause = KnowledgeClause(**clause.dict())
    db.add(db_clause)
    db.commit()
    db.refresh(db_clause)
    return db_clause

@router.get("/recommend")
def recommend_clauses(
    keywords: str = Query(..., description="关键词"),
    db: Session = Depends(get_db)
):
    """根据关键词推荐相关条款"""
    clauses = db.query(KnowledgeClause).filter(
        KnowledgeClause.keywords.contains(keywords)
    ).limit(5).all()

    return [
        {
            "id": clause.id,
            "standard_name": clause.standard_name,
            "clause_number": clause.clause_number,
            "title": clause.title,
            "relevance": "high"  # 简化的相关性评分
        }
        for clause in clauses
    ]
