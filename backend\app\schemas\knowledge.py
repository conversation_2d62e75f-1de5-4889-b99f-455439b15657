from pydantic import BaseModel
from typing import Optional


class KnowledgeClauseBase(BaseModel):
    standard_name: str
    clause_number: str
    title: str
    content: str
    keywords: Optional[str] = None
    meta_data: Optional[str] = None


class KnowledgeClauseCreate(KnowledgeClauseBase):
    pass


class KnowledgeClauseUpdate(BaseModel):
    standard_name: Optional[str] = None
    clause_number: Optional[str] = None
    title: Optional[str] = None
    content: Optional[str] = None
    keywords: Optional[str] = None
    meta_data: Optional[str] = None


class KnowledgeClauseResponse(KnowledgeClauseBase):
    id: int

    class Config:
        from_attributes = True
