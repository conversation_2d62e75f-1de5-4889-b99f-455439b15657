from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, documents, departments, knowledge

api_router = APIRouter()

# 认证相关路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])

# 用户管理路由
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])

# 文档管理路由
api_router.include_router(documents.router, prefix="/documents", tags=["文档管理"])

# 部门管理路由
api_router.include_router(departments.router, prefix="/departments", tags=["部门管理"])

# 知识库管理路由
api_router.include_router(knowledge.router, prefix="/knowledge", tags=["知识库管理"])
