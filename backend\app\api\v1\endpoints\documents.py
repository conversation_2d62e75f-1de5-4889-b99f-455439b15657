from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.core.database import get_db
from app.models.document import Document, DocumentStatus
from app.schemas.document import DocumentResponse, DocumentCreate, DocumentUpdate

router = APIRouter()

@router.get("/", response_model=List[DocumentResponse])
def get_documents(
    status: Optional[DocumentStatus] = Query(None, description="文档状态"),
    creator_id: Optional[int] = Query(None, description="创建者ID"),
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(100, description="限制数量"),
    db: Session = Depends(get_db)
):
    """获取文档列表"""
    query = db.query(Document)

    if status:
        query = query.filter(Document.status == status)

    if creator_id:
        query = query.filter(Document.creator_id == creator_id)

    documents = query.offset(skip).limit(limit).all()
    return documents

@router.get("/{document_id}", response_model=DocumentResponse)
def get_document(document_id: int, db: Session = Depends(get_db)):
    """获取单个文档"""
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="文档不存在")
    return document

@router.post("/", response_model=DocumentResponse)
def create_document(document: DocumentCreate, db: Session = Depends(get_db)):
    """创建文档"""
    # 生成文档编号
    doc_count = db.query(Document).count()
    doc_number = f"DOC-{doc_count + 1:04d}"

    db_document = Document(
        doc_number=doc_number,
        **document.dict()
    )
    db.add(db_document)
    db.commit()
    db.refresh(db_document)
    return db_document

@router.put("/{document_id}", response_model=DocumentResponse)
def update_document(
    document_id: int,
    document: DocumentUpdate,
    db: Session = Depends(get_db)
):
    """更新文档"""
    db_document = db.query(Document).filter(Document.id == document_id).first()
    if not db_document:
        raise HTTPException(status_code=404, detail="文档不存在")

    for field, value in document.dict(exclude_unset=True).items():
        setattr(db_document, field, value)

    db.commit()
    db.refresh(db_document)
    return db_document

@router.delete("/{document_id}")
def delete_document(document_id: int, db: Session = Depends(get_db)):
    """删除文档"""
    db_document = db.query(Document).filter(Document.id == document_id).first()
    if not db_document:
        raise HTTPException(status_code=404, detail="文档不存在")

    db.delete(db_document)
    db.commit()
    return {"message": "文档删除成功"}

@router.post("/generate")
def generate_document_draft(
    request: dict,
    db: Session = Depends(get_db)
):
    """
    生成文档草稿

    请求格式：
    {
        "title": "文档标题",
        "departments": ["部门1", "部门2"],
        "standards": ["标准1", "标准2"],
        "process_data": {
            "inputs": "输入",
            "outputs": "输出",
            "kpis": "KPI"
        }
    }
    """
    from app.services.ai_service import AIService

    ai_service = AIService()

    title = request.get("title", "")
    departments = request.get("departments", [])
    standards = request.get("standards", [])
    process_data = request.get("process_data", {})

    if not title:
        raise HTTPException(status_code=400, detail="文档标题不能为空")

    # 生成文档内容
    content = ai_service.generate_document_draft(
        title=title,
        departments=departments,
        standards=standards,
        process_data=process_data
    )

    return {
        "title": title,
        "content": content,
        "departments": departments,
        "standards": standards,
        "generated_at": "2025-07-31T09:30:00Z"
    }
