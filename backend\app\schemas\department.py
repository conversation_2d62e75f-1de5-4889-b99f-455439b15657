from pydantic import BaseModel
from typing import Optional, List


class DepartmentBase(BaseModel):
    name: str
    parent_id: Optional[int] = None


class DepartmentCreate(DepartmentBase):
    pass


class DepartmentUpdate(BaseModel):
    name: Optional[str] = None
    parent_id: Optional[int] = None


class DepartmentResponse(DepartmentBase):
    id: int

    class Config:
        from_attributes = True


class DepartmentTree(DepartmentResponse):
    children: List['DepartmentTree'] = []

    class Config:
        from_attributes = True
