from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

from app.core.config import settings
from app.core.database import engine, Base
from app.api.v1.api import api_router

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="工厂制度文件智能生成与管理平台",
    description="基于AI技术的制度文件智能生成系统",
    version="2.0.0"
)

# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
import os
if not os.path.exists("static"):
    os.makedirs("static")
app.mount("/static", StaticFiles(directory="static"), name="static")

# API路由
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    from fastapi.responses import FileResponse
    return FileResponse('static/index.html')

@app.get("/api")
async def api_root():
    return {"message": "工厂制度文件智能生成与管理平台 API"}


@app.get("/health")
async def health_check():
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    import sys

    print("正在启动工厂制度文件智能生成与管理平台...", flush=True)
    print("服务器地址: http://localhost:8000", flush=True)
    print("API文档: http://localhost:8000/docs", flush=True)
    print("按 Ctrl+C 停止服务器", flush=True)
    sys.stdout.flush()

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
