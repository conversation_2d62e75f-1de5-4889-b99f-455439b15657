# 工厂制度文件智能生成与管理平台 - 快速启动指南

## 项目概述

本项目是一个基于AI技术的工厂制度文件智能生成与管理系统，能够根据组织架构、人员信息和ISO标准、法律法规要求，自动生成各类管理文件。

## 技术栈

- **后端**: Python + FastAPI + SQLAlchemy + SQLite
- **前端**: Vue 3 + Element Plus + Vite
- **AI集成**: 支持多种AI模型接入

## 快速启动

### 1. 启动后端服务

```bash
# 进入后端目录
cd backend

# 安装依赖（如果还没安装）
python -m pip install fastapi uvicorn sqlalchemy psycopg2-binary alembic python-jose passlib python-multipart pydantic pydantic-settings requests python-dotenv bcrypt

# 初始化数据库（首次运行）
python init_data.py

# 启动服务器
python main.py
```

后端服务将在 http://localhost:8000 启动

### 2. 访问应用

- **API文档**: http://localhost:8000/docs
- **主页**: http://localhost:8000
- **健康检查**: http://localhost:8000/health

### 3. 默认账号

- **用户名**: admin
- **密码**: admin

## 主要功能

### ✅ 已实现功能

1. **用户认证系统**
   - 用户登录/注册
   - JWT令牌认证
   - 角色权限管理

2. **数据库模型**
   - 用户管理（User）
   - 部门管理（Department）
   - 文档管理（Document）
   - 知识库管理（KnowledgeClause）

3. **API接口**
   - 用户CRUD操作
   - 部门CRUD操作
   - 文档CRUD操作
   - 知识库CRUD操作
   - AI文档生成接口

4. **前端界面**
   - 登录页面
   - 主界面布局
   - 仪表盘
   - 文档创建向导（基础版）

5. **AI服务集成**
   - AI文档生成服务
   - 标准推荐功能
   - 模拟内容生成

### 🚧 开发中功能

1. **流程建模器**
   - BPMN.js集成
   - 可视化流程设计
   - 流程数据存储

2. **富文本编辑器**
   - 文档内容编辑
   - 格式化支持
   - 版本控制

3. **高级AI功能**
   - 真实AI模型集成
   - 智能推荐优化
   - 合规性分析

## API测试

### 登录测试
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin"
```

### 获取部门列表
```bash
curl -X GET "http://localhost:8000/api/v1/departments/"
```

### 生成文档测试
```bash
curl -X POST "http://localhost:8000/api/v1/documents/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "采购控制程序",
    "departments": ["采购部", "质量部"],
    "standards": ["ISO 9001:2015 8.4"],
    "process_data": {
      "inputs": "采购申请单",
      "outputs": "采购订单",
      "kpis": "及时率≥95%"
    }
  }'
```

## 数据库结构

系统使用SQLite数据库，包含以下主要表：

- `users` - 用户表
- `departments` - 部门表
- `documents` - 文档表
- `document_processes` - 文档过程表
- `knowledge_clauses` - 知识库条款表

## 开发说明

### 添加新的API端点

1. 在 `backend/app/api/v1/endpoints/` 中创建新的路由文件
2. 在 `backend/app/api/v1/api.py` 中注册路由
3. 创建对应的数据模型和schemas

### 添加新的前端页面

1. 在 `frontend/src/views/` 中创建Vue组件
2. 在 `frontend/src/router/index.js` 中添加路由
3. 在侧边栏菜单中添加导航

## 故障排除

### 后端启动问题

1. **依赖缺失**: 确保安装了所有必需的Python包
2. **端口占用**: 检查8000端口是否被占用
3. **数据库问题**: 删除 `zhidu.db` 文件并重新运行 `init_data.py`

### 前端问题

1. **Node.js未安装**: 需要安装Node.js 18+
2. **依赖安装**: 在frontend目录运行 `npm install`

## 下一步开发计划

1. 完善前端界面和交互
2. 集成真实的AI服务
3. 实现流程建模功能
4. 添加文档版本控制
5. 实现审批工作流
6. 添加数据导入导出功能

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目文档: 查看项目根目录下的文档文件
- 技术支持: 查看API文档和代码注释
