# 🖥️ 工厂制度文件智能生成平台 - 桌面版使用说明

## 🎯 桌面软件优势

相比网页版，桌面软件版本具有以下优势：

✅ **无需浏览器** - 独立运行，不依赖浏览器  
✅ **更好的用户体验** - 专为桌面设计的界面  
✅ **本地文件保存** - 直接保存到本地，方便管理  
✅ **快速模板** - 内置常用文档模板  
✅ **离线使用** - 启动后可以关闭命令行窗口  

## 🚀 快速启动

### 方法1: 一键启动完整系统
双击运行 `start_complete_system.bat`

这会自动：
1. 启动后端API服务
2. 检查并安装依赖
3. 启动桌面应用程序
4. 自动登录系统

### 方法2: 仅启动桌面应用
如果后端服务已经在运行，可以直接：
- 双击运行 `start_desktop_app.bat`
- 或者在命令行运行 `python desktop_app.py`

## 🎨 界面介绍

### 主界面布局
```
┌─────────────────────────────────────────────────────────┐
│                🏭 工厂制度文件智能生成平台                │
│              基于AI技术的工厂制度文件智能生成与管理系统      │
├─────────────────┬───────────────────────────────────────┤
│   🔐 系统状态    │                                      │
│   📝 文档生成    │         📄 文档显示区域                │
│   📋 快速模板    │                                      │
│                │                                      │
└─────────────────┴───────────────────────────────────────┘
```

### 左侧控制面板

#### 🔐 系统状态
- 显示当前登录状态
- 自动使用 admin/admin 登录
- 绿色表示已登录，红色表示未登录

#### 📝 文档生成
包含以下输入字段：
- **文档标题**: 要生成的文档名称
- **涉及部门**: 相关部门，用逗号分隔
- **关联标准**: ISO标准条款，用逗号分隔
- **过程输入**: 业务流程的输入
- **过程输出**: 业务流程的输出
- **关键绩效指标**: KPI指标

#### 📋 快速模板
预设了4个常用模板：
- **采购控制程序**
- **质量检验程序**
- **生产控制程序**
- **设备管理程序**

### 右侧文档显示区域
- 显示生成的文档内容
- 支持滚动查看长文档
- 包含文档信息和完整内容

## 📋 使用步骤

### 1. 启动应用
双击 `start_complete_system.bat` 启动完整系统

### 2. 选择生成方式

#### 方式A: 使用快速模板
1. 点击左侧任一模板按钮（如"质量检验程序"）
2. 系统会自动填充所有字段
3. 可以根据需要修改字段内容
4. 点击"🚀 生成文档"

#### 方式B: 自定义输入
1. 在各个输入框中填写您的要求
2. 确保文档标题不为空
3. 其他字段可以根据需要填写
4. 点击"🚀 生成文档"

### 3. 查看生成结果
- 文档会显示在右侧区域
- 包含文档标题、部门、标准等信息
- 显示完整的AI生成内容

### 4. 保存文档
- 点击"💾 保存文档"按钮
- 选择保存位置和文件名
- 文档会保存为文本文件

## 🎯 使用示例

### 示例1: 生成质量检验程序
1. 点击"质量检验程序"模板
2. 系统自动填充：
   - 标题: 质量检验程序
   - 部门: 质量部,生产部,技术部
   - 标准: ISO 9001:2015 8.5,ISO 9001:2015 8.6
   - 输入: 待检产品,检验标准
   - 输出: 检验报告,合格证明
   - KPI: 合格率≥99%,及时率≥100%
3. 点击"🚀 生成文档"
4. 查看生成的完整程序文件

### 示例2: 自定义采购程序
1. 手动输入：
   - 标题: 供应商评估程序
   - 部门: 采购部,质量部,技术部
   - 标准: ISO 9001:2015 8.4.1
   - 输入: 供应商申请,评估标准
   - 输出: 供应商评估报告,合格供应商名录
   - KPI: 评估及时率≥100%,合格率≥90%
2. 点击"🚀 生成文档"
3. 保存生成的文档

## 🔧 功能特点

### 🤖 AI智能生成
- 基于ISO 9001标准
- 自动生成专业文档结构
- 包含目的、范围、职责、程序等完整内容
- 符合质量管理体系要求

### 📊 模板系统
- 预设常用文档模板
- 一键加载标准配置
- 可以在模板基础上修改

### 💾 文档管理
- 本地文件保存
- 支持文本格式
- 包含完整文档信息
- 便于后续编辑和使用

### 🔄 实时生成
- 后台异步处理
- 实时状态更新
- 生成过程可视化

## ⚠️ 注意事项

### 系统要求
- Windows 操作系统
- Python 3.7 或更高版本
- 网络连接（用于AI生成）

### 使用建议
1. **首次使用**: 建议先使用快速模板熟悉功能
2. **网络连接**: 确保网络畅通，AI生成需要网络
3. **文档审核**: AI生成的内容建议人工审核后使用
4. **定期保存**: 及时保存重要文档到本地
5. **模板定制**: 可以基于模板修改出适合的配置

### 故障排除
1. **无法启动**: 检查Python是否正确安装
2. **连接失败**: 确认后端服务是否正常运行
3. **生成失败**: 检查网络连接和输入内容
4. **保存失败**: 确认有文件写入权限

## 🎉 开始使用

现在您可以：

1. **双击 `start_complete_system.bat`** 启动完整系统
2. **选择一个模板** 或 **自定义输入**
3. **点击生成文档** 体验AI智能生成
4. **保存文档** 到本地使用

享受高效的文档生成体验！🚀

## 📞 技术支持

如果遇到问题：
1. 检查命令行窗口的错误信息
2. 确认后端服务正常运行
3. 重启应用程序
4. 查看状态栏的提示信息
