#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于模板的AI制度文件生成器
根据用户提供的制度模板，生成符合ISO要求的制度文件
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import requests
import json
import threading
from datetime import datetime
import os

class TemplateBasedGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("🏭 基于模板的AI制度文件生成器")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f5f5f5')
        
        # API配置
        self.api_base = "http://localhost:8000"
        self.token = None
        
        # 模板存储
        self.current_template = ""
        self.generated_document = ""
        
        # 创建界面
        self.create_widgets()
        
        # 自动登录
        self.auto_login()
    
    def create_widgets(self):
        # 主标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=70)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🏭 基于模板的AI制度文件生成器", 
                              font=('Microsoft YaHei', 16, 'bold'), 
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(title_frame, text="上传您的制度模板，AI将生成符合ISO标准的专业制度文件", 
                                 font=('Microsoft YaHei', 10), 
                                 fg='#ecf0f1', bg='#2c3e50')
        subtitle_label.pack()
        
        # 主容器
        main_container = tk.Frame(self.root, bg='#f5f5f5')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 创建左右分栏
        # 左侧：模板输入区域
        left_frame = tk.Frame(main_container, bg='white', relief='raised', bd=1)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # 右侧：生成结果区域
        right_frame = tk.Frame(main_container, bg='white', relief='raised', bd=1)
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        # === 左侧内容 ===
        # 系统状态
        status_frame = tk.LabelFrame(left_frame, text="🔐 系统状态", font=('Microsoft YaHei', 10, 'bold'), bg='white')
        status_frame.pack(fill='x', padx=15, pady=15)
        
        self.status_label = tk.Label(status_frame, text="未连接", fg='red', font=('Microsoft YaHei', 9), bg='white')
        self.status_label.pack(pady=5)
        
        # 模板输入区域
        template_frame = tk.LabelFrame(left_frame, text="📝 制度模板输入", font=('Microsoft YaHei', 10, 'bold'), bg='white')
        template_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))
        
        # 模板操作按钮
        template_btn_frame = tk.Frame(template_frame, bg='white')
        template_btn_frame.pack(fill='x', padx=10, pady=10)
        
        load_btn = tk.Button(template_btn_frame, text="📁 加载模板文件", command=self.load_template_file,
                           bg='#3498db', fg='white', font=('Microsoft YaHei', 9))
        load_btn.pack(side='left', padx=(0, 5))
        
        clear_btn = tk.Button(template_btn_frame, text="🗑️ 清空", command=self.clear_template,
                            bg='#e74c3c', fg='white', font=('Microsoft YaHei', 9))
        clear_btn.pack(side='left', padx=5)
        
        sample_btn = tk.Button(template_btn_frame, text="📋 示例模板", command=self.load_sample_template,
                             bg='#f39c12', fg='white', font=('Microsoft YaHei', 9))
        sample_btn.pack(side='left', padx=5)
        
        # 模板文本输入
        tk.Label(template_frame, text="请输入或粘贴您的制度模板内容：", 
                font=('Microsoft YaHei', 9), bg='white').pack(anchor='w', padx=10, pady=(10, 5))
        
        self.template_text = scrolledtext.ScrolledText(template_frame, wrap=tk.WORD, 
                                                      font=('Microsoft YaHei', 10),
                                                      bg='#fafafa', fg='#333',
                                                      height=15)
        self.template_text.pack(fill='both', expand=True, padx=10, pady=(0, 10))
        
        # ISO标准选择
        iso_frame = tk.LabelFrame(left_frame, text="📊 ISO标准要求", font=('Microsoft YaHei', 10, 'bold'), bg='white')
        iso_frame.pack(fill='x', padx=15, pady=(0, 15))
        
        tk.Label(iso_frame, text="选择适用的ISO标准：", font=('Microsoft YaHei', 9), bg='white').pack(anchor='w', padx=10, pady=(10, 5))
        
        # ISO标准复选框
        self.iso_vars = {}
        iso_standards = [
            ("ISO 9001:2015 质量管理体系", "ISO_9001"),
            ("ISO 14001:2015 环境管理体系", "ISO_14001"),
            ("ISO 45001:2018 职业健康安全", "ISO_45001"),
            ("ISO 27001:2013 信息安全管理", "ISO_27001")
        ]
        
        for text, key in iso_standards:
            var = tk.BooleanVar()
            self.iso_vars[key] = var
            cb = tk.Checkbutton(iso_frame, text=text, variable=var, 
                               font=('Microsoft YaHei', 9), bg='white')
            cb.pack(anchor='w', padx=20, pady=2)
        
        # 默认选中ISO 9001
        self.iso_vars["ISO_9001"].set(True)
        
        # 生成按钮
        generate_btn = tk.Button(left_frame, text="🚀 生成符合ISO要求的制度文件", 
                               command=self.generate_document,
                               bg='#27ae60', fg='white', 
                               font=('Microsoft YaHei', 12, 'bold'),
                               height=2)
        generate_btn.pack(fill='x', padx=15, pady=(0, 15))
        
        # === 右侧内容 ===
        # 生成结果标题
        result_title = tk.Label(right_frame, text="📄 生成的制度文件", 
                               font=('Microsoft YaHei', 12, 'bold'), bg='white')
        result_title.pack(pady=15)
        
        # 文档信息显示
        self.doc_info_frame = tk.Frame(right_frame, bg='#ecf0f1', relief='sunken', bd=1)
        self.doc_info_frame.pack(fill='x', padx=15, pady=(0, 10))
        
        self.doc_info_label = tk.Label(self.doc_info_frame, text="等待生成文档...", 
                                      font=('Microsoft YaHei', 9), bg='#ecf0f1', fg='#7f8c8d')
        self.doc_info_label.pack(pady=10)
        
        # 生成结果文本显示
        self.result_text = scrolledtext.ScrolledText(right_frame, wrap=tk.WORD, 
                                                    font=('Microsoft YaHei', 10),
                                                    bg='#fafafa', fg='#2c3e50')
        self.result_text.pack(fill='both', expand=True, padx=15, pady=(0, 10))
        
        # 操作按钮
        action_frame = tk.Frame(right_frame, bg='white')
        action_frame.pack(fill='x', padx=15, pady=(0, 15))
        
        save_btn = tk.Button(action_frame, text="💾 保存文档", command=self.save_document,
                           bg='#3498db', fg='white', font=('Microsoft YaHei', 9))
        save_btn.pack(side='left', padx=(0, 5))
        
        copy_btn = tk.Button(action_frame, text="📋 复制内容", command=self.copy_content,
                           bg='#9b59b6', fg='white', font=('Microsoft YaHei', 9))
        copy_btn.pack(side='left', padx=5)
        
        export_btn = tk.Button(action_frame, text="📤 导出Word", command=self.export_word,
                             bg='#e67e22', fg='white', font=('Microsoft YaHei', 9))
        export_btn.pack(side='left', padx=5)
        
        # 状态栏
        self.status_bar = tk.Label(self.root, text="就绪 - 请输入制度模板", 
                                  relief=tk.SUNKEN, anchor=tk.W,
                                  font=('Microsoft YaHei', 8), bg='#ecf0f1')
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def auto_login(self):
        """自动登录到后端服务"""
        try:
            response = requests.post(f"{self.api_base}/api/v1/auth/login", 
                                   data={"username": "admin", "password": "admin"})
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                self.status_label.config(text="✅ 已连接到AI服务", fg='green')
                self.update_status("✅ 系统已就绪，可以开始生成文档")
            else:
                self.status_label.config(text="❌ 连接失败", fg='red')
                self.update_status("❌ 无法连接到AI服务，请检查后端是否启动")
        except Exception as e:
            self.status_label.config(text="❌ 服务不可用", fg='red')
            self.update_status(f"❌ 连接错误: {str(e)}")
    
    def update_status(self, message):
        """更新状态栏"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_bar.config(text=f"[{timestamp}] {message}")
        self.root.update()
    
    def load_template_file(self):
        """加载模板文件"""
        file_path = filedialog.askopenfilename(
            title="选择制度模板文件",
            filetypes=[
                ("文本文件", "*.txt"),
                ("Word文档", "*.docx"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            try:
                if file_path.endswith('.txt'):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                elif file_path.endswith('.docx'):
                    # 这里可以添加docx文件读取功能
                    messagebox.showinfo("提示", "Word文档支持功能开发中，请使用文本文件")
                    return
                else:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                
                self.template_text.delete(1.0, tk.END)
                self.template_text.insert(1.0, content)
                self.update_status(f"✅ 已加载模板文件: {os.path.basename(file_path)}")
                
            except Exception as e:
                messagebox.showerror("错误", f"无法读取文件: {str(e)}")
                self.update_status(f"❌ 文件读取失败: {str(e)}")
    
    def clear_template(self):
        """清空模板内容"""
        self.template_text.delete(1.0, tk.END)
        self.update_status("🗑️ 已清空模板内容")
    
    def load_sample_template(self):
        """加载示例模板"""
        sample_template = """采购控制程序

1. 目的
本程序旨在规范公司采购活动，确保采购的产品和服务满足规定要求。

2. 适用范围
本程序适用于公司所有采购活动，包括原材料、设备、服务等的采购。

3. 职责
3.1 采购部：负责采购计划制定、供应商管理、采购执行
3.2 质量部：负责供应商评估、进料检验
3.3 财务部：负责采购预算控制、付款审批

4. 工作程序
4.1 采购需求确认
4.2 供应商选择与评估
4.3 采购订单下达
4.4 进料检验与验收
4.5 付款与记录

5. 相关记录
- 采购申请单
- 供应商评估记录
- 采购订单
- 进料检验报告"""
        
        self.template_text.delete(1.0, tk.END)
        self.template_text.insert(1.0, sample_template)
        self.update_status("📋 已加载示例模板")
    
    def generate_document(self):
        """生成符合ISO要求的制度文件"""
        template_content = self.template_text.get(1.0, tk.END).strip()
        
        if not template_content:
            messagebox.showerror("错误", "请先输入制度模板内容")
            return
        
        if not self.token:
            messagebox.showerror("错误", "系统未连接，请检查后端服务")
            return
        
        # 获取选中的ISO标准
        selected_standards = []
        for key, var in self.iso_vars.items():
            if var.get():
                standard_names = {
                    "ISO_9001": "ISO 9001:2015",
                    "ISO_14001": "ISO 14001:2015", 
                    "ISO_45001": "ISO 45001:2018",
                    "ISO_27001": "ISO 27001:2013"
                }
                selected_standards.append(standard_names[key])
        
        if not selected_standards:
            messagebox.showwarning("警告", "请至少选择一个ISO标准")
            return
        
        self.update_status("🔄 正在生成符合ISO要求的制度文件...")
        
        # 在后台线程中生成文档
        threading.Thread(target=self._generate_document_thread, 
                        args=(template_content, selected_standards), 
                        daemon=True).start()
    
    def _generate_document_thread(self, template_content, standards):
        """后台生成文档"""
        try:
            # 构造请求数据
            data = {
                "title": "基于模板生成的制度文件",
                "template_content": template_content,
                "standards": standards,
                "requirements": "请根据提供的模板内容，生成一份符合ISO标准要求的正式制度文件。要求：1)保持原有结构和内容逻辑；2)补充完善ISO标准要求的要素；3)使用专业的制度文件语言；4)确保内容的完整性和合规性。"
            }
            
            headers = {"Content-Type": "application/json"}
            if self.token:
                headers["Authorization"] = f"Bearer {self.token}"
            
            # 调用专门的模板生成API
            response = requests.post(f"{self.api_base}/api/v1/documents/generate-from-template",
                                   json=data, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                self.root.after(0, self._update_result_display, result)
            else:
                error_msg = f"生成失败 (HTTP {response.status_code})"
                try:
                    error_detail = response.json().get("detail", "未知错误")
                    error_msg += f": {error_detail}"
                except:
                    pass
                self.root.after(0, lambda: self._show_error(error_msg))
        
        except requests.exceptions.Timeout:
            self.root.after(0, lambda: self._show_error("请求超时，请重试"))
        except Exception as e:
            self.root.after(0, lambda: self._show_error(f"生成失败: {str(e)}"))
    
    def _update_result_display(self, result):
        """更新结果显示"""
        # 更新文档信息
        info_text = f"📄 {result.get('title', '生成的制度文件')}\n"
        info_text += f"🏷️ 标准: {', '.join(result.get('standards', []))}\n"
        info_text += f"⏰ 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        self.doc_info_label.config(text=info_text, fg='#2c3e50')
        
        # 更新文档内容
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(1.0, result.get('content', ''))
        
        # 保存生成的文档
        self.generated_document = result.get('content', '')
        
        self.update_status("✅ 制度文件生成完成")
    
    def _show_error(self, error_msg):
        """显示错误信息"""
        self.update_status(f"❌ {error_msg}")
        messagebox.showerror("生成失败", error_msg)
    
    def save_document(self):
        """保存文档"""
        if not self.generated_document:
            messagebox.showwarning("警告", "没有可保存的文档内容")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[
                ("文本文件", "*.txt"),
                ("Word文档", "*.docx"),
                ("所有文件", "*.*")
            ]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.generated_document)
                self.update_status(f"✅ 文档已保存: {os.path.basename(filename)}")
                messagebox.showinfo("成功", f"文档已保存到:\n{filename}")
            except Exception as e:
                error_msg = f"保存失败: {str(e)}"
                self.update_status(f"❌ {error_msg}")
                messagebox.showerror("错误", error_msg)
    
    def copy_content(self):
        """复制内容到剪贴板"""
        if not self.generated_document:
            messagebox.showwarning("警告", "没有可复制的内容")
            return
        
        self.root.clipboard_clear()
        self.root.clipboard_append(self.generated_document)
        self.update_status("📋 内容已复制到剪贴板")
        messagebox.showinfo("成功", "文档内容已复制到剪贴板")
    
    def export_word(self):
        """导出为Word文档"""
        messagebox.showinfo("提示", "Word导出功能开发中，请使用保存为文本文件")

def main():
    root = tk.Tk()
    app = TemplateBasedGenerator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
