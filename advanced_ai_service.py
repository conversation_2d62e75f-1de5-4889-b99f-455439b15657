#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级AI制度体系文件生成服务
支持基于框架/关键词的智能文档生成，具备上下文感知和合规性检查功能
"""

import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

class OrganizationType(Enum):
    """组织类型"""
    GOVERNMENT = "government"  # 政府机构
    ENTERPRISE = "enterprise"  # 企业
    NGO = "ngo"  # 非营利组织
    INSTITUTION = "institution"  # 事业单位

class DocumentType(Enum):
    """文档类型"""
    REGULATION = "regulation"  # 企业规章
    POLICY = "policy"  # 政策文件
    PROCEDURE = "procedure"  # 操作流程
    STANDARD = "standard"  # 质量标准
    MANUAL = "manual"  # 管理手册
    GUIDELINE = "guideline"  # 指导原则

class Industry(Enum):
    """行业类型"""
    MANUFACTURING = "manufacturing"  # 制造业
    FINANCE = "finance"  # 金融业
    HEALTHCARE = "healthcare"  # 医疗健康
    EDUCATION = "education"  # 教育
    TECHNOLOGY = "technology"  # 科技
    RETAIL = "retail"  # 零售
    CONSTRUCTION = "construction"  # 建筑
    LOGISTICS = "logistics"  # 物流

@dataclass
class GenerationContext:
    """生成上下文"""
    organization_type: OrganizationType
    industry: Industry
    document_type: DocumentType
    organization_name: str
    department: Optional[str] = None
    region: str = "中国"
    language: str = "zh-CN"
    compliance_standards: List[str] = None

@dataclass
class ComplianceIssue:
    """合规性问题"""
    level: str  # "error", "warning", "info"
    message: str
    suggestion: str
    regulation_reference: str

@dataclass
class GenerationResult:
    """生成结果"""
    content: str
    metadata: Dict
    compliance_report: List[ComplianceIssue]
    confidence_score: float

class AdvancedAIService:
    """高级AI制度体系文件生成服务"""
    
    def __init__(self):
        self.knowledge_base = self._load_knowledge_base()
        self.templates = self._load_templates()
        self.regulations = self._load_regulations()
        
    def generate_document(self, 
                         framework: str,
                         keywords: List[str],
                         context: GenerationContext) -> GenerationResult:
        """
        基于框架和关键词生成制度文件
        
        Args:
            framework: 文档框架描述
            keywords: 关键词列表
            context: 生成上下文
            
        Returns:
            GenerationResult: 生成结果
        """
        # 1. 上下文分析
        analyzed_context = self._analyze_context(framework, keywords, context)
        
        # 2. 模板匹配
        matched_template = self._match_template(analyzed_context)
        
        # 3. 内容生成
        generated_content = self._generate_content(framework, keywords, analyzed_context, matched_template)
        
        # 4. 合规性检查
        compliance_report = self._check_compliance(generated_content, context)
        
        # 5. 质量评估
        confidence_score = self._calculate_confidence(generated_content, compliance_report)
        
        return GenerationResult(
            content=generated_content,
            metadata={
                "framework": framework,
                "keywords": keywords,
                "context": analyzed_context,
                "template_used": matched_template["name"],
                "generated_at": datetime.now().isoformat()
            },
            compliance_report=compliance_report,
            confidence_score=confidence_score
        )
    
    def _analyze_context(self, framework: str, keywords: List[str], context: GenerationContext) -> Dict:
        """分析生成上下文"""
        analysis = {
            "organization_type": context.organization_type.value,
            "industry": context.industry.value,
            "document_type": context.document_type.value,
            "key_themes": self._extract_themes(framework, keywords),
            "regulatory_requirements": self._get_regulatory_requirements(context),
            "template_preferences": self._get_template_preferences(context)
        }
        return analysis
    
    def _extract_themes(self, framework: str, keywords: List[str]) -> List[str]:
        """提取主题"""
        themes = []
        
        # 基于关键词提取主题
        theme_mapping = {
            "质量": ["质量管理", "质量控制", "质量保证"],
            "安全": ["安全管理", "职业健康", "安全生产"],
            "环境": ["环境保护", "环境管理", "绿色发展"],
            "人力": ["人力资源", "员工管理", "培训发展"],
            "财务": ["财务管理", "成本控制", "预算管理"],
            "采购": ["采购管理", "供应商管理", "合同管理"],
            "生产": ["生产管理", "工艺控制", "设备管理"],
            "销售": ["销售管理", "客户服务", "市场营销"]
        }
        
        for keyword in keywords:
            for theme_key, theme_values in theme_mapping.items():
                if theme_key in keyword:
                    themes.extend(theme_values)
        
        # 基于框架提取主题
        if "管理" in framework:
            themes.append("管理体系")
        if "流程" in framework:
            themes.append("流程管理")
        if "标准" in framework:
            themes.append("标准化")
            
        return list(set(themes))
    
    def _get_regulatory_requirements(self, context: GenerationContext) -> List[str]:
        """获取法规要求"""
        requirements = []
        
        # 基础法律法规
        requirements.extend([
            "《中华人民共和国公司法》",
            "《中华人民共和国劳动法》",
            "《中华人民共和国劳动合同法》"
        ])
        
        # 行业特定法规
        industry_regulations = {
            Industry.MANUFACTURING: [
                "《中华人民共和国安全生产法》",
                "《中华人民共和国环境保护法》",
                "《中华人民共和国产品质量法》"
            ],
            Industry.FINANCE: [
                "《中华人民共和国银行业监督管理法》",
                "《中华人民共和国证券法》",
                "《中华人民共和国保险法》"
            ],
            Industry.HEALTHCARE: [
                "《中华人民共和国药品管理法》",
                "《医疗器械监督管理条例》",
                "《医疗机构管理条例》"
            ]
        }
        
        if context.industry in industry_regulations:
            requirements.extend(industry_regulations[context.industry])
            
        # 组织类型特定法规
        if context.organization_type == OrganizationType.GOVERNMENT:
            requirements.extend([
                "《中华人民共和国公务员法》",
                "《中华人民共和国行政许可法》"
            ])
            
        return requirements
    
    def _get_template_preferences(self, context: GenerationContext) -> Dict:
        """获取模板偏好"""
        preferences = {
            "structure": "standard",
            "formality": "high",
            "detail_level": "comprehensive"
        }
        
        # 根据组织类型调整
        if context.organization_type == OrganizationType.GOVERNMENT:
            preferences["formality"] = "very_high"
            preferences["structure"] = "official"
        elif context.organization_type == OrganizationType.ENTERPRISE:
            preferences["formality"] = "high"
            preferences["structure"] = "business"
        elif context.organization_type == OrganizationType.NGO:
            preferences["formality"] = "medium"
            preferences["structure"] = "flexible"
            
        return preferences
    
    def _match_template(self, context: Dict) -> Dict:
        """匹配最适合的模板"""
        # 简化的模板匹配逻辑
        template_scores = {}
        
        for template in self.templates:
            score = 0
            
            # 文档类型匹配
            if template["document_type"] == context["document_type"]:
                score += 50
                
            # 行业匹配
            if context["industry"] in template.get("industries", []):
                score += 30
                
            # 主题匹配
            for theme in context["key_themes"]:
                if theme in template.get("themes", []):
                    score += 10
                    
            template_scores[template["id"]] = score
        
        # 选择得分最高的模板
        best_template_id = max(template_scores, key=template_scores.get)
        return next(t for t in self.templates if t["id"] == best_template_id)
    
    def _generate_content(self, framework: str, keywords: List[str], 
                         context: Dict, template: Dict) -> str:
        """生成文档内容"""
        
        # 构建生成提示
        prompt = self._build_generation_prompt(framework, keywords, context, template)
        
        # 模拟AI生成（实际应用中调用真实AI API）
        content = self._simulate_ai_generation(prompt, context, template)
        
        return content
    
    def _build_generation_prompt(self, framework: str, keywords: List[str], 
                                context: Dict, template: Dict) -> str:
        """构建生成提示"""
        prompt = f"""
请基于以下信息生成一份专业的制度文件：

框架描述：{framework}
关键词：{', '.join(keywords)}
组织类型：{context['organization_type']}
行业：{context['industry']}
文档类型：{context['document_type']}
主要主题：{', '.join(context['key_themes'])}

法规要求：
{chr(10).join(f"- {req}" for req in context['regulatory_requirements'])}

请确保生成的文件：
1. 结构完整，逻辑清晰
2. 符合相关法律法规要求
3. 适合{context['organization_type']}类型的组织
4. 体现{context['industry']}行业特点
5. 使用专业、规范的语言
6. 包含必要的实施细节

模板结构参考：{template['structure']}
"""
        return prompt
    
    def _simulate_ai_generation(self, prompt: str, context: Dict, template: Dict) -> str:
        """模拟AI生成内容"""
        org_type_names = {
            "government": "政府机构",
            "enterprise": "企业",
            "ngo": "非营利组织",
            "institution": "事业单位"
        }
        
        doc_type_names = {
            "regulation": "管理制度",
            "policy": "政策文件", 
            "procedure": "操作程序",
            "standard": "标准规范",
            "manual": "管理手册",
            "guideline": "指导原则"
        }
        
        content = f"""
{doc_type_names.get(context['document_type'], '制度文件')}

文件编号：[待分配]
版本号：V1.0
生效日期：{datetime.now().strftime('%Y年%m月%d日')}
制定部门：[制定部门]
适用范围：全{org_type_names.get(context['organization_type'], '组织')}

第一章 总则

第一条 目的和依据
为了{self._generate_purpose_statement(context)}，根据{', '.join(context['regulatory_requirements'][:3])}等法律法规，结合本{org_type_names.get(context['organization_type'], '组织')}实际情况，制定本制度。

第二条 适用范围
本制度适用于本{org_type_names.get(context['organization_type'], '组织')}及其所属部门的相关{context['document_type']}活动。

第三条 基本原则
（一）合法合规原则：严格遵守国家法律法规和行业标准；
（二）科学管理原则：建立科学、规范的管理体系；
（三）持续改进原则：不断优化和完善管理流程；
（四）责任明确原则：明确各级人员的职责和权限。

第二章 组织架构与职责

第四条 组织架构
{self._generate_organization_structure(context)}

第五条 职责分工
{self._generate_responsibilities(context)}

第三章 管理要求

第六条 基本要求
{self._generate_basic_requirements(context)}

第七条 操作流程
{self._generate_procedures(context)}

第八条 质量控制
{self._generate_quality_control(context)}

第四章 监督检查

第九条 内部监督
建立内部监督检查机制，定期对制度执行情况进行检查评估。

第十条 外部监督
接受上级部门和相关监管机构的监督检查，及时整改发现的问题。

第五章 奖惩措施

第十一条 奖励机制
对严格执行制度、成效显著的部门和个人给予表彰奖励。

第十二条 责任追究
对违反制度规定的行为，依据情节轻重给予相应处理。

第六章 附则

第十三条 制度解释
本制度由[制定部门]负责解释。

第十四条 生效时间
本制度自发布之日起施行。

附件：
1. 相关表格模板
2. 操作流程图
3. 法律法规清单

制定：[制定人]
审核：[审核人]
批准：[批准人]

{datetime.now().strftime('%Y年%m月%d日')}
"""
        return content.strip()
    
    def _generate_purpose_statement(self, context: Dict) -> str:
        """生成目的陈述"""
        purposes = {
            "regulation": "规范管理行为，提高管理效率",
            "policy": "明确政策导向，统一执行标准", 
            "procedure": "标准化操作流程，确保工作质量",
            "standard": "建立质量标准，保证产品服务质量",
            "manual": "完善管理体系，提升管理水平",
            "guideline": "提供指导原则，规范工作行为"
        }
        return purposes.get(context['document_type'], "规范相关工作，提高管理水平")
    
    def _generate_organization_structure(self, context: Dict) -> str:
        """生成组织架构"""
        if context['organization_type'] == 'enterprise':
            return """
本制度实行分级管理体系：
（一）决策层：董事会/总经理办公会
（二）管理层：各职能部门负责人
（三）执行层：各岗位工作人员
"""
        elif context['organization_type'] == 'government':
            return """
本制度实行统一领导、分级负责的管理体制：
（一）领导层：主要负责人
（二）管理层：分管领导和部门负责人  
（三）执行层：具体工作人员
"""
        else:
            return """
建立健全组织管理体系，明确各级职责分工。
"""
    
    def _generate_responsibilities(self, context: Dict) -> str:
        """生成职责分工"""
        return """
（一）主要负责人：对制度执行负总责，决策重大事项；
（二）分管领导：负责分管领域的制度执行和监督；
（三）部门负责人：负责本部门制度的具体实施；
（四）工作人员：严格按照制度要求履行职责。
"""
    
    def _generate_basic_requirements(self, context: Dict) -> str:
        """生成基本要求"""
        return f"""
（一）严格遵守国家法律法规和行业标准；
（二）建立健全{context['document_type']}管理制度；
（三）明确工作流程和操作规范；
（四）加强人员培训和能力建设；
（五）建立记录档案和追溯机制。
"""
    
    def _generate_procedures(self, context: Dict) -> str:
        """生成操作流程"""
        return """
（一）计划阶段：制定工作计划，明确目标任务；
（二）实施阶段：按照规定程序组织实施；
（三）检查阶段：定期检查执行情况；
（四）改进阶段：总结经验，持续改进。
"""
    
    def _generate_quality_control(self, context: Dict) -> str:
        """生成质量控制要求"""
        return """
（一）建立质量管理体系，明确质量标准；
（二）实施过程控制，确保各环节质量；
（三）开展质量检查，及时发现和纠正问题；
（四）持续改进质量管理，提升整体水平。
"""
    
    def _check_compliance(self, content: str, context: GenerationContext) -> List[ComplianceIssue]:
        """合规性检查"""
        issues = []
        
        # 检查必要章节
        required_sections = ["总则", "职责", "要求", "监督", "附则"]
        for section in required_sections:
            if section not in content:
                issues.append(ComplianceIssue(
                    level="warning",
                    message=f"缺少必要章节：{section}",
                    suggestion=f"建议添加{section}相关内容",
                    regulation_reference="制度文件编写规范"
                ))
        
        # 检查法规引用
        if "法律法规" not in content:
            issues.append(ComplianceIssue(
                level="error", 
                message="缺少法律法规依据",
                suggestion="应在总则中明确法律法规依据",
                regulation_reference="制度制定规范"
            ))
        
        # 检查生效条款
        if "生效" not in content and "施行" not in content:
            issues.append(ComplianceIssue(
                level="error",
                message="缺少生效条款",
                suggestion="应在附则中明确制度生效时间",
                regulation_reference="制度文件格式规范"
            ))
            
        return issues
    
    def _calculate_confidence(self, content: str, compliance_report: List[ComplianceIssue]) -> float:
        """计算置信度分数"""
        base_score = 0.8
        
        # 根据合规问题调整分数
        for issue in compliance_report:
            if issue.level == "error":
                base_score -= 0.1
            elif issue.level == "warning":
                base_score -= 0.05
                
        # 根据内容长度调整
        if len(content) < 500:
            base_score -= 0.1
        elif len(content) > 2000:
            base_score += 0.1
            
        return max(0.0, min(1.0, base_score))
    
    def _load_knowledge_base(self) -> Dict:
        """加载知识库"""
        return {
            "templates": [],
            "regulations": [],
            "best_practices": []
        }
    
    def _load_templates(self) -> List[Dict]:
        """加载模板库"""
        return [
            {
                "id": "enterprise_regulation_001",
                "name": "企业管理制度模板",
                "document_type": "regulation",
                "industries": ["manufacturing", "technology", "retail"],
                "themes": ["管理体系", "质量管理", "人力资源"],
                "structure": "standard_enterprise"
            },
            {
                "id": "government_policy_001", 
                "name": "政府政策文件模板",
                "document_type": "policy",
                "industries": ["government"],
                "themes": ["政策制定", "公共管理"],
                "structure": "official_government"
            }
        ]
    
    def _load_regulations(self) -> List[Dict]:
        """加载法规库"""
        return [
            {
                "name": "公司法",
                "category": "企业法律",
                "applicable_to": ["enterprise"],
                "key_requirements": ["公司治理", "股东权益", "董事责任"]
            }
        ]


# 使用示例
if __name__ == "__main__":
    # 创建AI服务实例
    ai_service = AdvancedAIService()

    # 设置生成上下文
    context = GenerationContext(
        organization_type=OrganizationType.ENTERPRISE,
        industry=Industry.MANUFACTURING,
        document_type=DocumentType.REGULATION,
        organization_name="某制造企业",
        department="质量管理部",
        region="中国",
        language="zh-CN",
        compliance_standards=["ISO 9001:2015", "GB/T 19001-2016"]
    )

    # 生成文档
    result = ai_service.generate_document(
        framework="建立完善的质量管理制度，确保产品质量符合标准要求",
        keywords=["质量管理", "产品检验", "不合格品控制", "持续改进"],
        context=context
    )

    print("生成的文档内容：")
    print(result.content)
    print(f"\n置信度分数：{result.confidence_score}")
    print(f"合规性问题数量：{len(result.compliance_report)}")
