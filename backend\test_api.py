"""
API功能测试脚本
"""
import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"✅ 健康检查: {response.status_code} - {response.json()}")
        return True
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def test_login():
    """测试登录功能"""
    print("\n🔍 测试登录功能...")
    try:
        data = {
            "username": "admin",
            "password": "admin"
        }
        response = requests.post(f"{BASE_URL}/api/v1/auth/login", data=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 登录成功: 获得token")
            return result.get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_departments():
    """测试部门API"""
    print("\n🔍 测试部门API...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/departments/")
        if response.status_code == 200:
            departments = response.json()
            print(f"✅ 部门列表: 共{len(departments)}个部门")
            for dept in departments[:3]:  # 显示前3个
                print(f"   - {dept['name']}")
            return True
        else:
            print(f"❌ 部门API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 部门API异常: {e}")
        return False

def test_knowledge():
    """测试知识库API"""
    print("\n🔍 测试知识库API...")
    try:
        # 测试获取知识库
        response = requests.get(f"{BASE_URL}/api/v1/knowledge/")
        if response.status_code == 200:
            clauses = response.json()
            print(f"✅ 知识库条款: 共{len(clauses)}条")
            
            # 测试推荐功能
            response = requests.get(f"{BASE_URL}/api/v1/knowledge/recommend?keywords=采购")
            if response.status_code == 200:
                recommendations = response.json()
                print(f"✅ 推荐功能: 找到{len(recommendations)}条推荐")
                return True
            else:
                print(f"❌ 推荐功能失败: {response.status_code}")
                return False
        else:
            print(f"❌ 知识库API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 知识库API异常: {e}")
        return False

def test_document_generation():
    """测试文档生成功能"""
    print("\n🔍 测试文档生成功能...")
    try:
        data = {
            "title": "采购控制程序",
            "departments": ["采购部", "质量部"],
            "standards": ["ISO 9001:2015 8.4"],
            "process_data": {
                "inputs": "采购申请单",
                "outputs": "采购订单",
                "kpis": "及时率≥95%"
            }
        }
        
        response = requests.post(f"{BASE_URL}/api/v1/documents/generate", json=data)
        if response.status_code == 200:
            result = response.json()
            content_length = len(result.get("content", ""))
            print(f"✅ 文档生成成功: 内容长度{content_length}字符")
            print(f"   标题: {result.get('title')}")
            print(f"   涉及部门: {', '.join(result.get('departments', []))}")
            return True
        else:
            print(f"❌ 文档生成失败: {response.status_code}")
            try:
                print(f"   错误信息: {response.json()}")
            except:
                print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 文档生成异常: {e}")
        return False

def test_documents_crud():
    """测试文档CRUD操作"""
    print("\n🔍 测试文档CRUD操作...")
    try:
        # 获取文档列表
        response = requests.get(f"{BASE_URL}/api/v1/documents/")
        if response.status_code == 200:
            documents = response.json()
            print(f"✅ 文档列表: 共{len(documents)}个文档")
            return True
        else:
            print(f"❌ 文档列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 文档CRUD异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 工厂制度文件智能生成与管理平台 - API测试")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    results = []
    
    # 执行测试
    results.append(("健康检查", test_health()))
    results.append(("登录功能", test_login() is not None))
    results.append(("部门API", test_departments()))
    results.append(("知识库API", test_knowledge()))
    results.append(("文档生成", test_document_generation()))
    results.append(("文档CRUD", test_documents_crud()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
    
    print("\n📝 访问地址:")
    print(f"   API文档: {BASE_URL}/docs")
    print(f"   主页: {BASE_URL}")
    print(f"   健康检查: {BASE_URL}/health")

if __name__ == "__main__":
    main()
