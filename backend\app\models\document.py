from sqlalchemy import Column, Integer, String, Text, DateTime, Foreign<PERSON>ey, Enum, Date
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.core.database import Base


class DocumentStatus(str, enum.Enum):
    DRAFT = "draft"
    IN_REVIEW = "in_review"
    PUBLISHED = "published"
    ARCHIVED = "archived"


class Document(Base):
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    doc_number = Column(String(50), unique=True, index=True)
    title = Column(String(200), nullable=False)
    version = Column(String(20), default="V1.0")
    status = Column(Enum(DocumentStatus), default=DocumentStatus.DRAFT)
    content = Column(Text)
    creator_id = Column(Integer, ForeignKey("users.id"))
    owner_id = Column(Integer, ForeignKey("users.id"))
    review_cycle_days = Column(Integer, default=365)
    next_review_date = Column(Date)
    parent_doc_id = Column(Integer, ForeignKey("documents.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    creator = relationship("User", foreign_keys=[creator_id], back_populates="created_documents")
    owner = relationship("User", foreign_keys=[owner_id], back_populates="owned_documents")
    parent_document = relationship("Document", remote_side=[id])
    process = relationship("DocumentProcess", back_populates="document", uselist=False)


class DocumentProcess(Base):
    __tablename__ = "document_processes"

    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), unique=True)
    inputs = Column(Text)  # JSON格式存储
    outputs = Column(Text)  # JSON格式存储
    kpis = Column(Text)  # JSON格式存储
    flowchart_data = Column(Text)  # JSON格式存储BPMN数据

    # 关系
    document = relationship("Document", back_populates="process")
