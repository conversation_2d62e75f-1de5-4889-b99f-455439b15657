#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业AI制度体系文件生成器
基于需求分析的完整实现，支持智能生成、协作管理、知识库集成
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog
import json
import threading
from datetime import datetime
# 先尝试导入，如果失败则使用简化版本
try:
    from advanced_ai_service import (
        AdvancedAIService, GenerationContext, OrganizationType,
        Industry, DocumentType, ComplianceIssue
    )
    ADVANCED_AI_AVAILABLE = True
except ImportError:
    ADVANCED_AI_AVAILABLE = False
    # 简化版本的枚举和类
    from enum import Enum
    from dataclasses import dataclass
    from typing import List, Dict

    class OrganizationType(Enum):
        GOVERNMENT = "government"
        ENTERPRISE = "enterprise"
        NGO = "ngo"
        INSTITUTION = "institution"

    class Industry(Enum):
        MANUFACTURING = "manufacturing"
        FINANCE = "finance"
        HEALTHCARE = "healthcare"
        EDUCATION = "education"
        TECHNOLOGY = "technology"
        RETAIL = "retail"
        CONSTRUCTION = "construction"
        LOGISTICS = "logistics"

    class DocumentType(Enum):
        REGULATION = "regulation"
        POLICY = "policy"
        PROCEDURE = "procedure"
        STANDARD = "standard"
        MANUAL = "manual"
        GUIDELINE = "guideline"

    @dataclass
    class GenerationContext:
        organization_type: OrganizationType
        industry: Industry
        document_type: DocumentType
        organization_name: str
        department: str = None
        region: str = "中国"
        language: str = "zh-CN"
        compliance_standards: List[str] = None

    @dataclass
    class ComplianceIssue:
        level: str
        message: str
        suggestion: str
        regulation_reference: str

    @dataclass
    class GenerationResult:
        content: str
        metadata: Dict
        compliance_report: List[ComplianceIssue]
        confidence_score: float

    class SimpleAIService:
        """简化版AI服务"""
        def generate_document(self, framework: str, keywords: List[str], context: GenerationContext) -> GenerationResult:
            # 简化的文档生成逻辑
            org_type_names = {
                OrganizationType.GOVERNMENT: "政府机构",
                OrganizationType.ENTERPRISE: "企业",
                OrganizationType.NGO: "非营利组织",
                OrganizationType.INSTITUTION: "事业单位"
            }

            doc_type_names = {
                DocumentType.REGULATION: "管理制度",
                DocumentType.POLICY: "政策文件",
                DocumentType.PROCEDURE: "操作程序",
                DocumentType.STANDARD: "标准规范",
                DocumentType.MANUAL: "管理手册",
                DocumentType.GUIDELINE: "指导原则"
            }

            content = f"""
{doc_type_names.get(context.document_type, '制度文件')}

文件编号：[待分配]
版本号：V1.0
生效日期：{datetime.now().strftime('%Y年%m月%d日')}
制定单位：{context.organization_name}
适用范围：全{org_type_names.get(context.organization_type, '组织')}

第一章 总则

第一条 目的和依据
{framework}

第二条 适用范围
本制度适用于{context.organization_name}的相关业务活动。

第三条 基本原则
（一）合法合规原则：严格遵守国家法律法规；
（二）科学管理原则：建立科学、规范的管理体系；
（三）持续改进原则：不断优化和完善管理流程。

第二章 组织架构与职责

第四条 组织架构
建立健全组织管理体系，明确各级职责分工。

第五条 职责分工
（一）主要负责人：对制度执行负总责；
（二）部门负责人：负责本部门制度的具体实施；
（三）工作人员：严格按照制度要求履行职责。

第三章 管理要求

第六条 基本要求
围绕{', '.join(keywords)}等关键领域，建立完善的管理制度。

第七条 操作流程
（一）计划阶段：制定工作计划，明确目标任务；
（二）实施阶段：按照规定程序组织实施；
（三）检查阶段：定期检查执行情况；
（四）改进阶段：总结经验，持续改进。

第四章 监督检查

第八条 内部监督
建立内部监督检查机制，定期对制度执行情况进行检查评估。

第五章 附则

第九条 制度解释
本制度由相关部门负责解释。

第十条 生效时间
本制度自发布之日起施行。

制定：[制定人]
审核：[审核人]
批准：[批准人]

{datetime.now().strftime('%Y年%m月%d日')}
"""

            # 简单的合规性检查
            compliance_report = []
            if "总则" not in content:
                compliance_report.append(ComplianceIssue(
                    level="warning",
                    message="建议添加总则章节",
                    suggestion="总则应包含目的、范围、原则等内容",
                    regulation_reference="制度编写规范"
                ))

            return GenerationResult(
                content=content.strip(),
                metadata={
                    "framework": framework,
                    "keywords": keywords,
                    "organization": context.organization_name,
                    "generated_at": datetime.now().isoformat()
                },
                compliance_report=compliance_report,
                confidence_score=0.85
            )

class ProfessionalDocumentGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("🏛️ AI制度体系文件生成系统")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f8f9fa')
        
        # 初始化AI服务
        if ADVANCED_AI_AVAILABLE:
            self.ai_service = AdvancedAIService()
        else:
            self.ai_service = SimpleAIService()
        
        # 当前项目数据
        self.current_project = None
        self.generated_documents = []
        
        # 创建界面
        self.create_menu()
        self.create_toolbar()
        self.create_main_interface()
        self.create_status_bar()
        
        # 初始化数据
        self.load_initial_data()
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建项目", command=self.new_project)
        file_menu.add_command(label="打开项目", command=self.open_project)
        file_menu.add_command(label="保存项目", command=self.save_project)
        file_menu.add_separator()
        file_menu.add_command(label="导出文档", command=self.export_document)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="撤销", command=self.undo)
        edit_menu.add_command(label="重做", command=self.redo)
        edit_menu.add_separator()
        edit_menu.add_command(label="查找替换", command=self.find_replace)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="合规性检查", command=self.compliance_check)
        tools_menu.add_command(label="版本比较", command=self.version_compare)
        tools_menu.add_command(label="知识库管理", command=self.knowledge_management)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用指南", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = tk.Frame(self.root, bg='#e9ecef', height=50)
        toolbar.pack(fill='x', padx=5, pady=2)
        toolbar.pack_propagate(False)
        
        # 工具按钮
        buttons = [
            ("📄 新建", self.new_document),
            ("📁 打开", self.open_document),
            ("💾 保存", self.save_document),
            ("🚀 AI生成", self.ai_generate),
            ("✅ 合规检查", self.compliance_check),
            ("👥 协作", self.collaboration),
            ("📤 导出", self.export_document),
            ("⚙️ 设置", self.settings)
        ]
        
        for text, command in buttons:
            btn = tk.Button(toolbar, text=text, command=command,
                           bg='#ffffff', relief='flat', padx=10, pady=5,
                           font=('Microsoft YaHei', 9))
            btn.pack(side='left', padx=2)
    
    def create_main_interface(self):
        """创建主界面"""
        # 主容器
        main_container = tk.PanedWindow(self.root, orient='horizontal', bg='#f8f9fa')
        main_container.pack(fill='both', expand=True, padx=5, pady=5)
        
        # 左侧面板
        left_panel = self.create_left_panel(main_container)
        main_container.add(left_panel, width=300)
        
        # 中央编辑区
        center_panel = self.create_center_panel(main_container)
        main_container.add(center_panel, width=700)
        
        # 右侧面板
        right_panel = self.create_right_panel(main_container)
        main_container.add(right_panel, width=300)
    
    def create_left_panel(self, parent):
        """创建左侧面板"""
        left_frame = tk.Frame(parent, bg='white', relief='raised', bd=1)
        
        # 项目导航器
        nav_frame = tk.LabelFrame(left_frame, text="📁 项目导航", font=('Microsoft YaHei', 10, 'bold'))
        nav_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # 项目树
        self.project_tree = ttk.Treeview(nav_frame, height=15)
        self.project_tree.pack(fill='both', expand=True, padx=5, pady=5)
        
        # 项目树滚动条
        tree_scroll = ttk.Scrollbar(nav_frame, orient='vertical', command=self.project_tree.yview)
        self.project_tree.configure(yscrollcommand=tree_scroll.set)
        tree_scroll.pack(side='right', fill='y')
        
        # 快速操作
        quick_frame = tk.LabelFrame(left_frame, text="⚡ 快速操作", font=('Microsoft YaHei', 10, 'bold'))
        quick_frame.pack(fill='x', padx=5, pady=5)
        
        quick_buttons = [
            ("📋 企业规章", lambda: self.quick_generate("regulation")),
            ("📜 政策文件", lambda: self.quick_generate("policy")),
            ("🔄 操作流程", lambda: self.quick_generate("procedure")),
            ("📊 质量标准", lambda: self.quick_generate("standard"))
        ]
        
        for text, command in quick_buttons:
            btn = tk.Button(quick_frame, text=text, command=command,
                           bg='#f8f9fa', width=20, font=('Microsoft YaHei', 9))
            btn.pack(fill='x', padx=5, pady=2)
        
        return left_frame
    
    def create_center_panel(self, parent):
        """创建中央编辑面板"""
        center_frame = tk.Frame(parent, bg='white', relief='raised', bd=1)
        
        # 标签页控制
        self.notebook = ttk.Notebook(center_frame)
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # AI生成向导标签页
        self.create_ai_wizard_tab()
        
        # 文档编辑器标签页
        self.create_editor_tab()
        
        return center_frame
    
    def create_ai_wizard_tab(self):
        """创建AI生成向导标签页"""
        wizard_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(wizard_frame, text="🧙‍♂️ AI生成向导")
        
        # 滚动容器
        canvas = tk.Canvas(wizard_frame, bg='white')
        scrollbar = ttk.Scrollbar(wizard_frame, orient='vertical', command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='white')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 基本信息
        basic_frame = tk.LabelFrame(scrollable_frame, text="📋 基本信息", font=('Microsoft YaHei', 11, 'bold'))
        basic_frame.pack(fill='x', padx=10, pady=10)
        
        # 组织信息
        tk.Label(basic_frame, text="组织名称:", font=('Microsoft YaHei', 10)).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.org_name_entry = tk.Entry(basic_frame, width=30, font=('Microsoft YaHei', 10))
        self.org_name_entry.grid(row=0, column=1, padx=5, pady=5)
        
        tk.Label(basic_frame, text="组织类型:", font=('Microsoft YaHei', 10)).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.org_type_combo = ttk.Combobox(basic_frame, width=28, font=('Microsoft YaHei', 10))
        self.org_type_combo['values'] = ('企业', '政府机构', '非营利组织', '事业单位')
        self.org_type_combo.grid(row=1, column=1, padx=5, pady=5)
        self.org_type_combo.set('企业')
        
        tk.Label(basic_frame, text="行业类型:", font=('Microsoft YaHei', 10)).grid(row=2, column=0, sticky='w', padx=5, pady=5)
        self.industry_combo = ttk.Combobox(basic_frame, width=28, font=('Microsoft YaHei', 10))
        self.industry_combo['values'] = ('制造业', '金融业', '医疗健康', '教育', '科技', '零售', '建筑', '物流')
        self.industry_combo.grid(row=2, column=1, padx=5, pady=5)
        self.industry_combo.set('制造业')
        
        tk.Label(basic_frame, text="文档类型:", font=('Microsoft YaHei', 10)).grid(row=3, column=0, sticky='w', padx=5, pady=5)
        self.doc_type_combo = ttk.Combobox(basic_frame, width=28, font=('Microsoft YaHei', 10))
        self.doc_type_combo['values'] = ('企业规章', '政策文件', '操作流程', '质量标准', '管理手册', '指导原则')
        self.doc_type_combo.grid(row=3, column=1, padx=5, pady=5)
        self.doc_type_combo.set('企业规章')
        
        # 生成参数
        params_frame = tk.LabelFrame(scrollable_frame, text="🎯 生成参数", font=('Microsoft YaHei', 11, 'bold'))
        params_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(params_frame, text="文档框架:", font=('Microsoft YaHei', 10)).pack(anchor='w', padx=5, pady=(5,0))
        self.framework_text = scrolledtext.ScrolledText(params_frame, height=4, font=('Microsoft YaHei', 10))
        self.framework_text.pack(fill='x', padx=5, pady=5)
        self.framework_text.insert('1.0', '请描述您要生成的制度文件的基本框架和主要内容...')
        
        tk.Label(params_frame, text="关键词 (用逗号分隔):", font=('Microsoft YaHei', 10)).pack(anchor='w', padx=5, pady=(5,0))
        self.keywords_entry = tk.Entry(params_frame, font=('Microsoft YaHei', 10))
        self.keywords_entry.pack(fill='x', padx=5, pady=5)
        self.keywords_entry.insert(0, '质量管理, 流程控制, 责任制度')
        
        # 高级选项
        advanced_frame = tk.LabelFrame(scrollable_frame, text="⚙️ 高级选项", font=('Microsoft YaHei', 11, 'bold'))
        advanced_frame.pack(fill='x', padx=10, pady=10)
        
        # 合规标准
        tk.Label(advanced_frame, text="合规标准:", font=('Microsoft YaHei', 10)).pack(anchor='w', padx=5, pady=(5,0))
        standards_frame = tk.Frame(advanced_frame)
        standards_frame.pack(fill='x', padx=5, pady=5)
        
        self.standards_vars = {}
        standards = [
            ('ISO 9001:2015', 'iso9001'),
            ('ISO 14001:2015', 'iso14001'),
            ('ISO 45001:2018', 'iso45001'),
            ('GB/T 19001-2016', 'gbt19001')
        ]
        
        for i, (text, key) in enumerate(standards):
            var = tk.BooleanVar()
            self.standards_vars[key] = var
            cb = tk.Checkbutton(standards_frame, text=text, variable=var, font=('Microsoft YaHei', 9))
            cb.grid(row=i//2, column=i%2, sticky='w', padx=5, pady=2)
        
        # 生成按钮
        generate_btn = tk.Button(scrollable_frame, text="🚀 开始AI智能生成", 
                               command=self.start_ai_generation,
                               bg='#28a745', fg='white', 
                               font=('Microsoft YaHei', 12, 'bold'),
                               height=2)
        generate_btn.pack(fill='x', padx=10, pady=20)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_editor_tab(self):
        """创建文档编辑器标签页"""
        editor_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(editor_frame, text="📝 文档编辑器")
        
        # 编辑器工具栏
        editor_toolbar = tk.Frame(editor_frame, bg='#f8f9fa', height=40)
        editor_toolbar.pack(fill='x', padx=5, pady=5)
        editor_toolbar.pack_propagate(False)
        
        # 格式化按钮
        format_buttons = [
            ("B", self.bold_text),
            ("I", self.italic_text),
            ("U", self.underline_text),
            ("|", None),
            ("📋", self.paste_text),
            ("🔍", self.find_text),
            ("📊", self.insert_table)
        ]
        
        for text, command in format_buttons:
            if text == "|":
                separator = tk.Frame(editor_toolbar, width=2, bg='#dee2e6')
                separator.pack(side='left', fill='y', padx=5)
            else:
                btn = tk.Button(editor_toolbar, text=text, command=command,
                               bg='white', relief='flat', width=3,
                               font=('Microsoft YaHei', 9, 'bold'))
                btn.pack(side='left', padx=2)
        
        # 文档编辑器
        self.editor_text = scrolledtext.ScrolledText(editor_frame, wrap=tk.WORD, 
                                                    font=('Microsoft YaHei', 11),
                                                    bg='white', fg='#333')
        self.editor_text.pack(fill='both', expand=True, padx=5, pady=(0, 5))
    
    def create_right_panel(self, parent):
        """创建右侧面板"""
        right_frame = tk.Frame(parent, bg='white', relief='raised', bd=1)
        
        # 属性面板
        props_frame = tk.LabelFrame(right_frame, text="📋 文档属性", font=('Microsoft YaHei', 10, 'bold'))
        props_frame.pack(fill='x', padx=5, pady=5)
        
        # 文档信息显示
        self.doc_info_text = scrolledtext.ScrolledText(props_frame, height=8, 
                                                      font=('Microsoft YaHei', 9),
                                                      bg='#f8f9fa')
        self.doc_info_text.pack(fill='x', padx=5, pady=5)
        
        # 合规性检查面板
        compliance_frame = tk.LabelFrame(right_frame, text="✅ 合规性检查", font=('Microsoft YaHei', 10, 'bold'))
        compliance_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.compliance_tree = ttk.Treeview(compliance_frame, columns=('level', 'message'), height=10)
        self.compliance_tree.heading('#0', text='类型')
        self.compliance_tree.heading('level', text='级别')
        self.compliance_tree.heading('message', text='说明')
        self.compliance_tree.pack(fill='both', expand=True, padx=5, pady=5)
        
        # 智能建议面板
        suggestions_frame = tk.LabelFrame(right_frame, text="💡 智能建议", font=('Microsoft YaHei', 10, 'bold'))
        suggestions_frame.pack(fill='x', padx=5, pady=5)
        
        self.suggestions_text = scrolledtext.ScrolledText(suggestions_frame, height=6,
                                                         font=('Microsoft YaHei', 9),
                                                         bg='#fff3cd')
        self.suggestions_text.pack(fill='x', padx=5, pady=5)
        
        return right_frame
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = tk.Frame(self.root, bg='#e9ecef', height=25)
        self.status_bar.pack(side='bottom', fill='x')
        self.status_bar.pack_propagate(False)
        
        self.status_label = tk.Label(self.status_bar, text="就绪", 
                                    bg='#e9ecef', font=('Microsoft YaHei', 9))
        self.status_label.pack(side='left', padx=10)
        
        self.progress_bar = ttk.Progressbar(self.status_bar, length=200)
        self.progress_bar.pack(side='right', padx=10, pady=3)
    
    def load_initial_data(self):
        """加载初始数据"""
        # 初始化项目树
        self.project_tree.insert('', 'end', text='我的项目', open=True)
        self.project_tree.insert('', 'end', text='模板库', open=True)
        self.project_tree.insert('', 'end', text='法规库', open=True)
        
        # 设置默认状态
        self.update_status("系统已就绪，可以开始创建制度文件")
    
    def update_status(self, message):
        """更新状态栏"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_label.config(text=f"[{timestamp}] {message}")
        self.root.update()
    
    def start_ai_generation(self):
        """开始AI生成"""
        # 获取用户输入
        org_name = self.org_name_entry.get().strip()
        framework = self.framework_text.get('1.0', tk.END).strip()
        keywords_str = self.keywords_entry.get().strip()
        
        if not org_name or not framework or not keywords_str:
            messagebox.showerror("错误", "请填写完整的基本信息和生成参数")
            return
        
        # 解析输入
        keywords = [k.strip() for k in keywords_str.split(',') if k.strip()]
        
        # 映射选择到枚举
        org_type_map = {
            '企业': OrganizationType.ENTERPRISE,
            '政府机构': OrganizationType.GOVERNMENT,
            '非营利组织': OrganizationType.NGO,
            '事业单位': OrganizationType.INSTITUTION
        }
        
        industry_map = {
            '制造业': Industry.MANUFACTURING,
            '金融业': Industry.FINANCE,
            '医疗健康': Industry.HEALTHCARE,
            '教育': Industry.EDUCATION,
            '科技': Industry.TECHNOLOGY,
            '零售': Industry.RETAIL,
            '建筑': Industry.CONSTRUCTION,
            '物流': Industry.LOGISTICS
        }
        
        doc_type_map = {
            '企业规章': DocumentType.REGULATION,
            '政策文件': DocumentType.POLICY,
            '操作流程': DocumentType.PROCEDURE,
            '质量标准': DocumentType.STANDARD,
            '管理手册': DocumentType.MANUAL,
            '指导原则': DocumentType.GUIDELINE
        }
        
        # 创建生成上下文
        context = GenerationContext(
            organization_type=org_type_map[self.org_type_combo.get()],
            industry=industry_map[self.industry_combo.get()],
            document_type=doc_type_map[self.doc_type_combo.get()],
            organization_name=org_name
        )
        
        # 在后台线程中生成
        self.update_status("🔄 AI正在智能生成制度文件...")
        self.progress_bar.start()
        
        threading.Thread(target=self._ai_generation_thread, 
                        args=(framework, keywords, context), 
                        daemon=True).start()
    
    def _ai_generation_thread(self, framework, keywords, context):
        """AI生成后台线程"""
        try:
            # 调用AI服务生成文档
            result = self.ai_service.generate_document(framework, keywords, context)
            
            # 在主线程中更新UI
            self.root.after(0, self._update_generation_result, result)
            
        except Exception as e:
            self.root.after(0, self._show_generation_error, str(e))
    
    def _update_generation_result(self, result):
        """更新生成结果"""
        self.progress_bar.stop()
        
        # 更新编辑器内容
        self.editor_text.delete('1.0', tk.END)
        self.editor_text.insert('1.0', result.content)
        
        # 切换到编辑器标签页
        self.notebook.select(1)
        
        # 更新文档属性
        self._update_document_properties(result)
        
        # 更新合规性检查结果
        self._update_compliance_results(result.compliance_report)
        
        # 更新智能建议
        self._update_suggestions(result)
        
        self.update_status(f"✅ 文档生成完成，置信度: {result.confidence_score:.2%}")
    
    def _update_document_properties(self, result):
        """更新文档属性"""
        props_text = f"""文档标题: {result.metadata.get('framework', '未知')}
生成时间: {result.metadata.get('generated_at', '未知')}
使用模板: {result.metadata.get('template_used', '未知')}
置信度分数: {result.confidence_score:.2%}
内容长度: {len(result.content)} 字符
合规问题: {len(result.compliance_report)} 项
"""
        self.doc_info_text.delete('1.0', tk.END)
        self.doc_info_text.insert('1.0', props_text)
    
    def _update_compliance_results(self, compliance_report):
        """更新合规性检查结果"""
        # 清空现有内容
        for item in self.compliance_tree.get_children():
            self.compliance_tree.delete(item)
        
        # 添加合规性检查结果
        for i, issue in enumerate(compliance_report):
            level_icon = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}.get(issue.level, "❓")
            self.compliance_tree.insert('', 'end', 
                                      text=f"{level_icon} {issue.regulation_reference}",
                                      values=(issue.level, issue.message))
    
    def _update_suggestions(self, result):
        """更新智能建议"""
        suggestions = []
        
        # 基于合规性问题生成建议
        for issue in result.compliance_report:
            suggestions.append(f"• {issue.suggestion}")
        
        # 基于置信度生成建议
        if result.confidence_score < 0.7:
            suggestions.append("• 建议增加更多具体的业务细节")
            suggestions.append("• 考虑参考行业最佳实践")
        
        if not suggestions:
            suggestions.append("• 文档质量良好，建议进行人工审核后发布")
        
        suggestions_text = "\n".join(suggestions)
        self.suggestions_text.delete('1.0', tk.END)
        self.suggestions_text.insert('1.0', suggestions_text)
    
    def _show_generation_error(self, error_msg):
        """显示生成错误"""
        self.progress_bar.stop()
        self.update_status(f"❌ 生成失败: {error_msg}")
        messagebox.showerror("生成失败", f"AI生成过程中出现错误:\n{error_msg}")
    
    # 实现基本功能方法
    def new_project(self):
        """新建项目"""
        self.update_status("创建新项目")
        messagebox.showinfo("新建项目", "新建项目功能")

    def open_project(self):
        """打开项目"""
        file_path = filedialog.askopenfilename(
            title="打开项目文件",
            filetypes=[("项目文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            self.update_status(f"打开项目: {file_path}")

    def save_project(self):
        """保存项目"""
        file_path = filedialog.asksaveasfilename(
            title="保存项目",
            defaultextension=".json",
            filetypes=[("项目文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            self.update_status(f"保存项目: {file_path}")

    def export_document(self):
        """导出文档"""
        content = self.editor_text.get('1.0', tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "没有可导出的文档内容")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出文档",
            defaultextension=".txt",
            filetypes=[
                ("文本文件", "*.txt"),
                ("Word文档", "*.docx"),
                ("PDF文件", "*.pdf"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.update_status(f"文档已导出: {file_path}")
                messagebox.showinfo("成功", f"文档已导出到:\n{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")

    def new_document(self):
        """新建文档"""
        self.editor_text.delete('1.0', tk.END)
        self.update_status("新建文档")

    def open_document(self):
        """打开文档"""
        file_path = filedialog.askopenfilename(
            title="打开文档",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.editor_text.delete('1.0', tk.END)
                self.editor_text.insert('1.0', content)
                self.update_status(f"打开文档: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"打开失败: {str(e)}")

    def save_document(self):
        """保存文档"""
        content = self.editor_text.get('1.0', tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "没有可保存的内容")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存文档",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.update_status(f"文档已保存: {file_path}")
                messagebox.showinfo("成功", "文档保存成功")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def ai_generate(self):
        """AI生成（跳转到生成向导）"""
        self.notebook.select(0)  # 切换到AI生成向导标签页
        self.update_status("请在AI生成向导中填写参数")

    def compliance_check(self):
        """合规性检查"""
        content = self.editor_text.get('1.0', tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "没有可检查的内容")
            return

        # 简单的合规性检查
        issues = []
        required_sections = ["总则", "职责", "要求", "监督", "附则"]
        for section in required_sections:
            if section not in content:
                issues.append(f"缺少必要章节：{section}")

        if not issues:
            messagebox.showinfo("合规性检查", "✅ 文档结构完整，未发现明显问题")
        else:
            issue_text = "\n".join(f"• {issue}" for issue in issues)
            messagebox.showwarning("合规性检查", f"发现以下问题:\n{issue_text}")

        self.update_status("合规性检查完成")

    def collaboration(self):
        """协作功能"""
        messagebox.showinfo("协作功能", "协作功能正在开发中")

    def settings(self):
        """设置"""
        messagebox.showinfo("系统设置", "设置功能正在开发中")

    def undo(self):
        """撤销"""
        try:
            self.editor_text.edit_undo()
        except:
            pass

    def redo(self):
        """重做"""
        try:
            self.editor_text.edit_redo()
        except:
            pass

    def find_replace(self):
        """查找替换"""
        messagebox.showinfo("查找替换", "查找替换功能正在开发中")

    def version_compare(self):
        """版本比较"""
        messagebox.showinfo("版本比较", "版本比较功能正在开发中")

    def knowledge_management(self):
        """知识库管理"""
        messagebox.showinfo("知识库管理", "知识库管理功能正在开发中")

    def show_help(self):
        """显示帮助"""
        help_text = """
🏛️ AI制度体系文件生成系统 - 使用帮助

基本操作：
1. 在AI生成向导中填写组织信息和生成参数
2. 点击"开始AI智能生成"按钮
3. 在文档编辑器中查看和编辑生成的内容
4. 使用合规性检查验证文档质量
5. 导出或保存最终文档

快捷键：
- Ctrl+N: 新建文档
- Ctrl+O: 打开文档
- Ctrl+S: 保存文档
- Ctrl+Z: 撤销
- Ctrl+Y: 重做

如需更多帮助，请查看完整使用指南。
"""
        messagebox.showinfo("使用帮助", help_text)

    def show_about(self):
        """关于"""
        about_text = """
🏛️ AI制度体系文件生成系统
版本: 1.0.0
开发时间: 2025年7月

功能特色：
• 基于框架/关键词的AI智能生成
• 支持多种组织类型和行业
• 自动合规性检查
• 专业的文档编辑功能

技术架构：
• Python + Tkinter 桌面应用
• AI驱动的内容生成
• 模块化设计架构
"""
        messagebox.showinfo("关于系统", about_text)

    def quick_generate(self, doc_type):
        """快速生成"""
        # 根据文档类型设置默认值
        type_mapping = {
            "regulation": ("企业规章", "建立规范的企业管理制度", "管理制度, 规章制度, 企业管理"),
            "policy": ("政策文件", "制定明确的政策指导文件", "政策制定, 指导原则, 执行标准"),
            "procedure": ("操作流程", "规范标准化的操作流程", "操作流程, 标准化, 流程管理"),
            "standard": ("质量标准", "建立完善的质量标准体系", "质量标准, 标准体系, 质量管理")
        }

        if doc_type in type_mapping:
            doc_name, framework, keywords = type_mapping[doc_type]
            self.doc_type_combo.set(doc_name)
            self.framework_text.delete('1.0', tk.END)
            self.framework_text.insert('1.0', framework)
            self.keywords_entry.delete(0, tk.END)
            self.keywords_entry.insert(0, keywords)

            # 切换到AI生成向导
            self.notebook.select(0)
            self.update_status(f"已加载{doc_name}快速模板")

    def bold_text(self):
        """加粗文本"""
        try:
            self.editor_text.tag_add("bold", "sel.first", "sel.last")
            self.editor_text.tag_config("bold", font=('Microsoft YaHei', 11, 'bold'))
        except:
            pass

    def italic_text(self):
        """斜体文本"""
        try:
            self.editor_text.tag_add("italic", "sel.first", "sel.last")
            self.editor_text.tag_config("italic", font=('Microsoft YaHei', 11, 'italic'))
        except:
            pass

    def underline_text(self):
        """下划线文本"""
        try:
            self.editor_text.tag_add("underline", "sel.first", "sel.last")
            self.editor_text.tag_config("underline", underline=True)
        except:
            pass

    def paste_text(self):
        """粘贴文本"""
        try:
            clipboard_content = self.root.clipboard_get()
            self.editor_text.insert(tk.INSERT, clipboard_content)
        except:
            pass

    def find_text(self):
        """查找文本"""
        search_term = tk.simpledialog.askstring("查找", "请输入要查找的文本:")
        if search_term:
            content = self.editor_text.get('1.0', tk.END)
            if search_term in content:
                start_pos = content.find(search_term)
                if start_pos != -1:
                    # 计算行列位置
                    lines_before = content[:start_pos].count('\n')
                    col = start_pos - content.rfind('\n', 0, start_pos) - 1
                    start_index = f"{lines_before + 1}.{col}"
                    end_index = f"{lines_before + 1}.{col + len(search_term)}"

                    self.editor_text.tag_remove("found", "1.0", tk.END)
                    self.editor_text.tag_add("found", start_index, end_index)
                    self.editor_text.tag_config("found", background="yellow")
                    self.editor_text.see(start_index)
            else:
                messagebox.showinfo("查找结果", "未找到指定文本")

    def insert_table(self):
        """插入表格"""
        table_text = """
┌─────────────┬─────────────┬─────────────┐
│    项目     │    内容     │    备注     │
├─────────────┼─────────────┼─────────────┤
│             │             │             │
├─────────────┼─────────────┼─────────────┤
│             │             │             │
└─────────────┴─────────────┴─────────────┘
"""
        self.editor_text.insert(tk.INSERT, table_text)

def main():
    root = tk.Tk()
    app = ProfessionalDocumentGenerator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
